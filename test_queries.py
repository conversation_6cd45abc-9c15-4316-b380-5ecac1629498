#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能家居语音指令测试脚本
包含所有测试查询数据
"""

import requests
import json
import time
from datetime import datetime
import random

# 测试查询数据
TEST_QUERIES = [
    # 天气查询
    "北京的天气怎么样",
    "深圳的天气怎么样", 
    "顺德的天气怎么样",
    "佛山的天气怎么样",
    "广东的天气怎么样",
    "广州市南沙区的天气怎么样",
    "今天天气怎么样",
    "明天天气怎么样",
    "后天天气怎么样",
    "未来一周天气怎么样",
    "未来两周天气怎么样",
    "过去一周天气怎么样",
    "过去两周天气怎么样",
    "深圳今天的天气怎么样",
    "深圳明天的天气怎么样",
    "深圳后天的天气怎么样",
    "广东今天的天气怎么样",
    "深圳市南山区今天的天气怎么样",
    
    # 天气详细信息
    "今天的温度",
    "今天的日出时间",
    "今天的日落时间", 
    "今天的空气湿度",
    "今天的空气质量",
    "今天的风速",
    "今天的限行信息",
    "广州的限行信息",
    "今天广州的限行信息",
    
    # 生活指数
    "化妆指数",
    "紫外线指数",
    "感冒指数",
    "洗车指数",
    "穿衣指数",
    "运动指数",
    "钓鱼指数",
    "交通指数",
    "过敏指数",
    
    # 灯光控制 - 开启
    "打开主卧的筒灯一",
    "打开主卧的筒灯",
    "打开明装筒灯",
    "打开筒灯一",
    "打开全屋的明装筒灯",
    
    # 灯光控制 - 关闭
    "关闭主卧的筒灯一",
    "关闭主卧的明装筒灯",
    "关闭明装筒灯",
    "关闭过道三开",
    "关闭全屋的明装筒灯",
    
    # 灯光亮度调节 - 调高
    "主卧的吸顶灯一亮度调高一点",
    "主卧的明装筒灯亮度调高一点",
    "主卧的灯亮度调高一点",
    "主卧的吸顶灯一亮度调到最高",
    "主卧的明装筒灯亮度调到最高",
    "主卧的灯亮度调到最高",
    "调高吸顶灯一的亮度",
    "调高明装筒灯的亮度",
    "调高全屋的明装筒灯的亮度",
    "调高全屋灯的亮度",
    "吸顶灯一的亮度调到最高",
    "明装筒灯的亮度调到最高",
    "全屋的明装筒灯的亮度调到最高",
    "全屋灯的亮度调到最高",
    
    # 灯光亮度调节 - 调低
    "主卧的吸顶灯一亮度调低一点",
    "主卧的明装筒灯亮度调低一点",
    "主卧的灯亮度调低一点",
    "主卧的吸顶灯一亮度调到最低",
    "主卧的明装筒灯亮度调到最低",
    "主卧的灯亮度调到最低",
    "调低吸顶灯一的亮度",
    "调低明装筒灯的亮度",
    "调低全屋明装筒灯的亮度",
    "调低全屋灯的亮度",
    "吸顶灯一的亮度调到最低",
    "明装筒灯的亮度调到最低",
    "全屋明装筒灯的亮度调到最低",
    "全屋灯的亮度调到最低",
    
    # 灯光亮度设置
    "主卧的明装筒灯一亮度设成80%",
    "主卧的明装筒灯亮度设成80%",
    "主卧的灯亮度亮度设成80%",
    "明装筒灯一亮度设成80%",
    "明装筒灯亮度为80%",
    "全屋的明装筒灯亮度设成80%",
    "全屋灯的亮度设成80%",
    
    # 色温调节 - 调高
    "主卧的明装筒灯一色温调高一点",
    "主卧的明装筒灯色温调高一点",
    "主卧的明装筒灯一色温调到最高",
    "主卧的明装筒灯色温调到最高",
    "主卧色温调高一点",
    "明装筒灯一的色温调高一点",
    "主卧色温调到最高",
    "明装筒灯一的色温调到最高",
    "明装筒灯的色温调高一点",
    "调高全屋明装筒灯的色温",
    "明装筒灯的色温调到最高",
    "全屋明装筒灯的色温调到最高",
    "调高全屋灯的色温",
    
    # 色温调节 - 调低
    "主卧的明装筒灯一色温调低一点",
    "主卧的明装筒灯色温调低一点",
    "主卧的明装筒灯一色温调到最低",
    "主卧的明装筒灯色温调到最低",
    "主卧色温调低一点",
    "明装筒灯一的色温调低一点",
    "主卧色温调到最低",
    "明装筒灯一的色温调到最低",
    "明装筒灯的色温调低一点",
    "调低全屋明装筒灯的色温",
    "调低全屋灯的色温",
    "全屋明装筒灯的色温调到最低",
    "全屋灯的色温调到最低",
    
    # 色温设置
    "主卧的明装筒灯一色温设成3700",
    "主卧的明装筒灯色温设成3700",
    "主卧色温设成3700",
    "明装筒灯一的色温设成3700",
    "明装筒灯的色温设成3700",
    "全屋明装筒灯的色温设成3700",
    "全屋灯的色温设成3700",
    
    # 通断器控制
    "打开主卧的一路通断器",
    "打开主卧的通断器",
    "打开通断器",
    "打开通断器一",
    "打开全屋的通断器",
    "打开6路通断器灯一",
    "打开海上的灯",
    "关闭主卧的一路通断器",
    "关闭主卧的通断器",
    "关闭通断器",
    "关闭通断器一",
    "关闭全屋的通断器",
    "关闭6路通断器灯一",
    
    # 窗帘控制 - 开关
    "打开主卧的纱帘",
    "打开主卧的布帘",
    "打开主卧的窗帘电机",
    "打开窗帘电机",
    "打开纱帘",
    "打开布帘",
    "打开窗帘",
    "打开全屋的窗帘",
    "关闭主卧的纱帘",
    "关闭主卧的布帘",
    "关闭主卧的窗帘电机",
    "关闭窗帘电机",
    "关闭纱帘",
    "关闭布帘",
    "关上窗帘",
    "关闭全屋的窗帘",
    
    # 窗帘控制 - 暂停
    "暂停主卧的纱帘",
    "暂停主卧的布帘",
    "暂停主卧的窗帘电机",
    "暂停窗帘电机",
    "暂停纱帘",
    "暂停布帘",
    "暂停窗帘",
    "暂停全屋的窗帘",
    
    # 窗帘开合度设置
    "主卧的纱帘开合度设成80%",
    "主卧的布帘开合度设成80%",
    "主卧的窗帘电机开合度设成80%",
    "主卧的开合度设成80%",
    "窗帘电机开合度设成80%",
    "纱帘开合度设成80%",
    "布帘开合度设成80%",
    "全屋的开合度设成80%",
    
    # 窗帘面板控制
    "打开主卧的窗帘面板一",
    "打开主卧的窗帘面板",
    "打开窗帘面板",
    "打开窗帘面板一",
    "打开全屋的窗帘面板",
    "打开窗帘一",
    "关闭主卧的窗帘面板一",
    "关闭主卧的窗帘面板",
    "关闭窗帘面板",
    "关闭窗帘面板一",
    "关闭全屋的窗帘面板",
    "关闭窗帘一",
    "暂停主卧的窗帘面板一",
    "暂停主卧的窗帘面板",
    "暂停窗帘面板",
    "暂停窗帘面板一",
    "暂停全屋的窗帘面板",
    "暂停窗帘一",
    
    # 情景模式
    "回家模式",
    "到家了",
    "回来了",
    "回家了",
    "离家模式",
    "我走了",
    "出门",
    "上班去了",
    "外出",
    "我离家了",
    "旅游",
    "用餐模式",
    "我要吃饭了",
    "会客模式",
    "有客人来了",
    "来客人了",
    "晨起模式",
    "我要起床了",
    "我醒了",
    "睡眠模式",
    "我要睡觉了",
    "执行全开模式",
    "执行全关模式",

    # 空调控制 - 开关
    "打开主卧的空调一",
    "打开主卧的空调",
    "打开主卧的主卧空调",
    "打开主卧空调",
    "打开客厅东空调",
    "打开客厅西空调",
    "打开空调",
    "打开全家的空调",
    "关闭主卧的空调一",
    "关闭主卧的空调",
    "关闭空调",
    "关闭全家的空调",

    # 空调温度控制
    "主卧的空调一的温度设成26度",
    "主卧的空调温度设成26度",
    "空调温度设成26度",
    "空调一的温度设成26度",
    "全家的空调温度设成26度",
    "主卧的主卧空调的温度调高一点",
    "主卧的空调的温度调高一点",
    "空调一的温度调高一点",
    "空调的温度调高一点",
    "全家的空调的温度调高一点",
    "主卧的主卧空调的温度调到最高",
    "主卧的空调的温度调到最高",
    "空调一的温度调到最高",
    "空调的温度调到最高",
    "全家的空调的温度调到最高",
    "主卧的主卧空调的温度调低一点",
    "主卧的空调的温度调低一点",
    "空调一的温度调低一点",
    "空调的温度调低一点",
    "全家的空调的温度调低一点",
    "主卧的主卧空调的温度调到最低",
    "主卧的空调的温度调到最低",
    "空调一的温度调到最低",
    "空调的温度调到最低",
    "全家的空调的温度调到最低",

    # 空调模式控制
    "空调一的工作模式设成制冷模式",
    "空调一的工作模式设成制热模式",
    "空调一的工作模式设成送风模式",
    "空调一的工作模式设成换气模式",
    "空调一的工作模式设成除湿模式",
    "主卧的主卧空调的模式设成制冷模式",
    "主卧的空调设成制冷模式",
    "空调设成制冷模式",
    "空调一设成制冷模式",
    "全家的空调设成制冷模式",

    # 空调风速控制
    "空调一的风速设成高速",
    "空调一的风速设成中速",
    "空调一的风速设成低速",
    "空调一的风速设成自动",
    "主卧的主卧空调的风速设成自动",
    "主卧的空调的风速设成自动",
    "空调的风速设成自动",
    "空调一的风速设成自动",
    "全家的空调的风速设成自动",

    # 开关控制
    "打开次卧的开关一",
    "打开次卧的开关",
    "打开客厅的过道三开",
    "打开客厅的餐厅筒灯",
    "打开客厅的筒灯",
    "打开客厅的灯",
    "打开开关",
    "打开开关一",
    "打开全家的开关",
    "关闭主卧的主卧双开",
    "关闭主卧的开关",
    "关闭开关",
    "关闭开关一",
    "关闭全家的开关",
    "打开书房的开关三",
    "打开\"餐厅筒灯\"",
    "关闭书房的\"餐厅筒灯\"",
    "关闭客厅的餐厅筒灯",
    "关闭客厅的筒灯",
    "关闭客厅的灯",
    "关闭开关三",

    # 插座控制
    "打开主卧的插座一",
    "打开主卧的插座",
    "打开插座一",
    "打开插座",
    "打开全家的插座",
    "关闭主卧的插座一",
    "关闭主卧的插座",
    "关闭插座一",
    "关闭插座",
    "关闭全家的插座",

    # 地暖控制
    "打开主卧的地暖一",
    "打开主卧的地暖",
    "打开地暖一",
    "打开地暖",
    "打开全家的地暖",
    "关闭主卧的地暖一",
    "关闭主卧的地暖",
    "关闭地暖一",
    "关闭地暖",
    "关闭全家的地暖",

    # 新风控制
    "打开主卧的新风",
    "打开新风",
    "打开新风一",
    "打开全家的新风机",
    "关闭主卧的新风",
    "关闭新风",
    "关闭新风一",
    "关闭全家的新风机",
    "主卧新风的风速设成自动",
    "主卧新风的风速设成高",
    "主卧新风的风速设成中",
    "主卧新风的风速设成低",
    "新风机风速调高",
    "新风机风速调低",
    "新风机打开杀菌",
    "新风机关闭杀菌",

    # 安防控制
    "打开安防",
    "关闭安防",

    # 语音助手
    "小零小零",
    "查询天气",
    "关闭全家的灯",
    "查询我的设备数量",
    "退下吧",
    "重新开始",
    "介绍几个景点给我",
    "你是谁",

    # 厨房设备控制
    "油烟机风速调大一点",
    "电饭煲开始煮饭",
    "微波炉剩余烹饪时长",
    "洗衣机调到快速洗",
    "新风机设为睡眠模式",
    "关闭卧室所有灯",
    "按摩椅开始按摩",
    "打开加湿器",
    "打开投影",

    # 组合控制
    "灯亮度和色温调低一点",
    "灯亮度和色温调高一点",
    "灯亮度调到80%色温调到3700k",
    "打开主卧的灯然后亮度和色温调低一点",
    "打开主卧的灯然后亮度和色温调高一点",
    "打开主卧的灯然后亮度调到80%色温调到3700k",
    "打开全家的灯并把亮度调到80%色温调到3700k",
    "空调的温度调低一点并调成制冷",
    "空调的温度调到26度模式调成制热",
    "空调的温度调到26度模式调成制热风速调高",
    "客厅空调的温度调到26度模式调成制热风速调高",
    "打开客厅的空调然后温度调到26度模式调成制热风速调高",
    "打开全家的空调然后温度调到26度模式调成制热风速调高",
    "打开灯和空调",
    "打开灯和地暖",
    "打开灯和新风",
    "打开空调和新风",
    "打开主卧的灯和空调",
    "打开全家的灯和空调",
    "打开灯和空调还有窗帘",
    "打开主卧的灯和空调还有窗帘",
    "打开全家的灯和空调还有窗帘",
    "打开主卧的所有设备",
    "打开氛围灯然后亮度调低色温调低",
    "打开氛围灯然后亮度调到50色温调到4000",
    "打开空调一工作模式调成制热温度调到26度风速调成高速",
]

def get_request_payload(query):
    """构造请求负载"""
    return {
        "query": query,
        "traceId": f"test_{int(time.time())}_{random.randint(1000, 9999)}",
        "context": {
            "scenes": [
                "测试专用",
                "会客模式",
                "离家模式",
                "睡眠模式",
                "用餐模式",
                "回家模式",
                "晨起模式",
                "全开模式",
                "全关模式"
            ],
            "rooms": [
                "客厅",
                "主卧",
                "客卧",
                "次卧",
                "书房",
                "卫生间",
                "餐厅",
                "厨房",
                "阳台",
                "走廊",
                "玄关",
                "儿童房",
                "过道",
                "全屋",
                "海上"
            ],
            "devices": [
                # 网关和安防设备
                {
                    "name": "Zero3",
                    "domain": "网关",
                    "space_name": "客厅"
                },
                {
                    "name": "Zero3",
                    "domain": "安防",
                    "space_name": "客厅"
                },
                # 主卧灯具设备
                {
                    "name": "筒灯一",
                    "domain": "灯",
                    "space_name": "主卧"
                },
                {
                    "name": "筒灯",
                    "domain": "灯",
                    "space_name": "主卧"
                },
                {
                    "name": "明装筒灯",
                    "domain": "灯",
                    "space_name": "主卧"
                },
                {
                    "name": "明装筒灯一",
                    "domain": "灯",
                    "space_name": "主卧"
                },
                {
                    "name": "吸顶灯一",
                    "domain": "灯",
                    "space_name": "主卧"
                },
                {
                    "name": "氛围灯",
                    "domain": "灯",
                    "space_name": "主卧"
                },
                # 全屋灯具设备
                {
                    "name": "全屋明装筒灯",
                    "domain": "灯",
                    "space_name": "全屋"
                },
                {
                    "name": "全屋灯",
                    "domain": "灯",
                    "space_name": "全屋"
                },
                # 过道设备
                {
                    "name": "过道三开",
                    "domain": "开关",
                    "space_name": "过道"
                },
                # 通断器设备
                {
                    "name": "一路通断器",
                    "domain": "开关",
                    "space_name": "主卧"
                },
                {
                    "name": "通断器",
                    "domain": "开关",
                    "space_name": "主卧"
                },
                {
                    "name": "通断器一",
                    "domain": "开关",
                    "space_name": "主卧"
                },
                {
                    "name": "6路通断器灯一",
                    "domain": "开关",
                    "space_name": "主卧"
                },
                {
                    "name": "全屋通断器",
                    "domain": "开关",
                    "space_name": "全屋"
                },
                # 海上设备（特殊测试场景）
                {
                    "name": "海上的灯",
                    "domain": "灯",
                    "space_name": "海上"
                },
                # 窗帘设备
                {
                    "name": "纱帘",
                    "domain": "窗帘",
                    "space_name": "主卧"
                },
                {
                    "name": "布帘",
                    "domain": "窗帘",
                    "space_name": "主卧"
                },
                {
                    "name": "窗帘电机",
                    "domain": "窗帘",
                    "space_name": "主卧"
                },
                {
                    "name": "窗帘",
                    "domain": "窗帘",
                    "space_name": "主卧"
                },
                {
                    "name": "全屋窗帘",
                    "domain": "窗帘",
                    "space_name": "全屋"
                },
                {
                    "name": "窗帘面板一",
                    "domain": "窗帘",
                    "space_name": "主卧"
                },
                {
                    "name": "窗帘面板",
                    "domain": "窗帘",
                    "space_name": "主卧"
                },
                {
                    "name": "窗帘一",
                    "domain": "窗帘",
                    "space_name": "主卧"
                },
                {
                    "name": "全屋窗帘面板",
                    "domain": "窗帘",
                    "space_name": "全屋"
                },
                # 空调设备
                {
                    "name": "空调一",
                    "domain": "空调",
                    "space_name": "主卧"
                },
                {
                    "name": "空调",
                    "domain": "空调",
                    "space_name": "主卧"
                },
                {
                    "name": "主卧空调",
                    "domain": "空调",
                    "space_name": "主卧"
                },
                {
                    "name": "客厅东空调",
                    "domain": "空调",
                    "space_name": "客厅"
                },
                {
                    "name": "客厅西空调",
                    "domain": "空调",
                    "space_name": "客厅"
                },
                {
                    "name": "客厅空调",
                    "domain": "空调",
                    "space_name": "客厅"
                },
                {
                    "name": "全家空调",
                    "domain": "空调",
                    "space_name": "全屋"
                },
                # 开关设备
                {
                    "name": "开关一",
                    "domain": "开关",
                    "space_name": "次卧"
                },
                {
                    "name": "开关",
                    "domain": "开关",
                    "space_name": "次卧"
                },
                {
                    "name": "餐厅筒灯",
                    "domain": "灯",
                    "space_name": "客厅"
                },
                {
                    "name": "筒灯",
                    "domain": "灯",
                    "space_name": "客厅"
                },
                {
                    "name": "客厅灯",
                    "domain": "灯",
                    "space_name": "客厅"
                },
                {
                    "name": "全家开关",
                    "domain": "开关",
                    "space_name": "全屋"
                },
                {
                    "name": "主卧双开",
                    "domain": "开关",
                    "space_name": "主卧"
                },
                {
                    "name": "开关三",
                    "domain": "开关",
                    "space_name": "书房"
                },
                # 插座设备
                {
                    "name": "插座一",
                    "domain": "插座",
                    "space_name": "主卧"
                },
                {
                    "name": "插座",
                    "domain": "插座",
                    "space_name": "主卧"
                },
                {
                    "name": "全家插座",
                    "domain": "插座",
                    "space_name": "全屋"
                },
                # 地暖设备
                {
                    "name": "地暖一",
                    "domain": "地暖",
                    "space_name": "主卧"
                },
                {
                    "name": "地暖",
                    "domain": "地暖",
                    "space_name": "主卧"
                },
                {
                    "name": "全家地暖",
                    "domain": "地暖",
                    "space_name": "全屋"
                },
                # 新风设备
                {
                    "name": "新风",
                    "domain": "新风机",
                    "space_name": "主卧"
                },
                {
                    "name": "新风一",
                    "domain": "新风机",
                    "space_name": "主卧"
                },
                {
                    "name": "新风机",
                    "domain": "新风机",
                    "space_name": "主卧"
                },
                {
                    "name": "全家新风机",
                    "domain": "新风机",
                    "space_name": "全屋"
                },
                # 厨房设备
                {
                    "name": "油烟机",
                    "domain": "油烟机",
                    "space_name": "厨房"
                },
                {
                    "name": "电饭煲",
                    "domain": "电饭煲",
                    "space_name": "厨房"
                },
                {
                    "name": "微波炉",
                    "domain": "微波炉",
                    "space_name": "厨房"
                },
                # 洗衣设备
                {
                    "name": "洗衣机",
                    "domain": "洗衣机",
                    "space_name": "阳台"
                },
                # 其他设备
                {
                    "name": "按摩椅",
                    "domain": "按摩椅",
                    "space_name": "客厅"
                },
                {
                    "name": "加湿器",
                    "domain": "加湿器",
                    "space_name": "客厅"
                },
                {
                    "name": "投影",
                    "domain": "投影仪",
                    "space_name": "客厅"
                }
            ],
            "candidate": {
                "domains": [],
                "devices": []
            },
            "homeGraph": {
                "name": "我的家庭",
                "id": "1283",
                "position": {
                    "adName": "",
                    "cityName": "",
                    "pName": "",
                    "cityCode": "440600",
                    "latitude": "22.928231957573892",
                    "adCode": "440606102",
                    "pCode": "440000",
                    "name": "广东省佛山市顺德区北滘镇",
                    "longitude": "113.20699596198394",
                    "typeCode": ""
                }
            }
        }
    }

def test_single_query(query, base_url="http://127.0.0.1:8578", endpoint="/send_msg"):
    """测试单个查询"""
    url = base_url + endpoint
    payload = get_request_payload(query)
    headers = {"Content-Type": "application/json"}

    print(f"\n🧪 测试查询: {query}")
    print("-" * 60)

    try:
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        end_time = time.time()
        request_time = end_time - start_time

        print(f"⏱️  请求耗时: {request_time:.2f}秒")
        print(f"📊 响应状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                response_data = response.json()
                print("✅ 请求成功!")

                if response_data.get("code") == "SUCCESS":
                    data = response_data.get("data", {})
                    if "inference_time" in data:
                        print(f"🤖 模型推理时间: {data['inference_time']:.2f}秒")

                    # 构造标准格式的响应
                    formatted_response = {
                        "code": "SUCCESS",
                        "msg": "成功",
                        "data": {
                            "response": data.get("response", []),
                            "inference_time": data.get("inference_time", 0)
                        }
                    }

                    print(f"💬 模型响应: {json.dumps(formatted_response, ensure_ascii=False, indent=2)}")
                    return True, formatted_response, request_time
                else:
                    error_response = {
                        "code": response_data.get("code", "ERROR"),
                        "msg": response_data.get("msg", "未知错误"),
                        "data": None
                    }
                    print(f"❌ API返回错误: {response_data.get('msg', '未知错误')}")
                    return False, error_response, request_time

            except json.JSONDecodeError as e:
                error_response = {
                    "code": "ERROR",
                    "msg": "JSON解析失败",
                    "data": None
                }
                print(f"❌ JSON解析失败: {e}")
                return False, error_response, request_time

        else:
            error_response = {
                "code": "ERROR",
                "msg": f"HTTP请求失败，状态码: {response.status_code}",
                "data": None
            }
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            return False, error_response, request_time

    except requests.exceptions.Timeout:
        error_response = {
            "code": "ERROR",
            "msg": "请求超时 (30秒)",
            "data": None
        }
        print("❌ 请求超时 (30秒)")
        return False, error_response, 30.0  # 超时时间
    except requests.exceptions.ConnectionError:
        error_response = {
            "code": "ERROR",
            "msg": "连接失败，请检查服务是否启动",
            "data": None
        }
        print("❌ 连接失败，请检查服务是否启动")
        return False, error_response, 0.0  # 连接失败时间为0
    except Exception as e:
        error_response = {
            "code": "ERROR",
            "msg": f"未知错误: {str(e)}",
            "data": None
        }
        print(f"❌ 未知错误: {e}")
        return False, error_response, 0.0  # 其他错误时间为0

def write_results_to_file(results, filename="test_queries.txt"):
    """将测试结果写入文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"智能家居语音指令测试结果\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总测试数量: {len(results)}\n")
            f.write("=" * 80 + "\n\n")

            for i, result in enumerate(results, 1):
                f.write(f"[{i}] 查询: {result['query']}\n")
                f.write(f"状态: {'✅ 成功' if result['success'] else '❌ 失败'}\n")
                f.write(f"请求耗时: {result.get('request_time', 0):.2f}秒\n")
                f.write(f"响应: {json.dumps(result['response'], ensure_ascii=False, indent=2)}\n")
                f.write("-" * 60 + "\n\n")

        print(f"📝 测试结果已写入文件: {filename}")
        return True
    except Exception as e:
        print(f"❌ 写入文件失败: {e}")
        return False

def test_all_queries(base_url="http://127.0.0.1:8578", endpoint="/send_msg", delay=1):
    """测试所有查询"""
    print("=" * 80)
    print(f"🚀 开始批量测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 总测试数量: {len(TEST_QUERIES)}")
    print("=" * 80)

    results = []
    success_count = 0

    for i, query in enumerate(TEST_QUERIES, 1):
        print(f"\n[{i}/{len(TEST_QUERIES)}]", end="")
        success, response, request_time = test_single_query(query, base_url, endpoint)

        results.append({
            "query": query,
            "success": success,
            "response": response,
            "request_time": request_time
        })

        if success:
            success_count += 1

        # 添加延迟避免请求过快
        if delay > 0 and i < len(TEST_QUERIES):
            time.sleep(delay)

    # 输出统计结果
    print("\n" + "=" * 80)
    print("📊 测试统计结果")
    print("=" * 80)
    print(f"✅ 成功: {success_count}/{len(TEST_QUERIES)} ({success_count/len(TEST_QUERIES)*100:.1f}%)")
    print(f"❌ 失败: {len(TEST_QUERIES)-success_count}/{len(TEST_QUERIES)} ({(len(TEST_QUERIES)-success_count)/len(TEST_QUERIES)*100:.1f}%)")

    # 输出失败的查询
    failed_queries = [r for r in results if not r["success"]]
    if failed_queries:
        print(f"\n❌ 失败的查询 ({len(failed_queries)}个):")
        for i, result in enumerate(failed_queries, 1):
            print(f"{i}. {result['query']}")
            if result["response"].get("msg"):
                print(f"   错误: {result['response']['msg']}")

    # 将结果写入文件
    write_results_to_file(results)

    return results

def test_category_queries(category_keywords, base_url="http://127.0.0.1:8578", endpoint="/send_msg", delay=1):
    """测试特定类别的查询"""
    filtered_queries = []
    for query in TEST_QUERIES:
        if any(keyword in query for keyword in category_keywords):
            filtered_queries.append(query)

    print("=" * 80)
    print(f"🎯 分类测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔍 关键词: {', '.join(category_keywords)}")
    print(f"📊 匹配查询数量: {len(filtered_queries)}")
    print("=" * 80)

    if not filtered_queries:
        print("❌ 没有找到匹配的查询")
        return []

    results = []
    success_count = 0

    for i, query in enumerate(filtered_queries, 1):
        print(f"\n[{i}/{len(filtered_queries)}]", end="")
        success, response, request_time = test_single_query(query, base_url, endpoint)

        results.append({
            "query": query,
            "success": success,
            "response": response,
            "request_time": request_time
        })

        if success:
            success_count += 1

        if delay > 0 and i < len(filtered_queries):
            time.sleep(delay)

    # 输出统计结果
    print("\n" + "=" * 80)
    print("📊 分类测试统计结果")
    print("=" * 80)
    print(f"✅ 成功: {success_count}/{len(filtered_queries)} ({success_count/len(filtered_queries)*100:.1f}%)")
    print(f"❌ 失败: {len(filtered_queries)-success_count}/{len(filtered_queries)} ({(len(filtered_queries)-success_count)/len(filtered_queries)*100:.1f}%)")

    # 将结果写入文件
    category_name = "_".join(category_keywords)
    filename = f"test_queries_{category_name}.txt"
    write_results_to_file(results, filename)

    return results

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "all":
            # 测试所有查询
            test_all_queries()
        elif sys.argv[1] == "weather":
            # 测试天气相关查询
            test_category_queries(["天气", "温度", "日出", "日落", "湿度", "空气", "风速", "限行", "指数"])
        elif sys.argv[1] == "light":
            # 测试灯光相关查询
            test_category_queries(["灯", "亮度", "色温", "筒灯", "吸顶灯", "氛围灯"])
        elif sys.argv[1] == "curtain":
            # 测试窗帘相关查询
            test_category_queries(["窗帘", "纱帘", "布帘", "开合度", "暂停"])
        elif sys.argv[1] == "ac":
            # 测试空调相关查询
            test_category_queries(["空调", "温度", "制冷", "制热", "风速"])
        elif sys.argv[1] == "scene":
            # 测试情景模式查询
            test_category_queries(["模式", "回家", "离家", "睡眠", "用餐", "会客", "晨起"])
        else:
            # 测试单个查询
            query = " ".join(sys.argv[1:])
            success, response, request_time = test_single_query(query)
            results = [{
                "query": query,
                "success": success,
                "response": response,
                "request_time": request_time
            }]
            write_results_to_file(results, "test_single_query.txt")
    else:
        # 默认测试前10个查询
        print("🧪 默认测试前10个查询")
        print("💡 使用方法:")
        print("  python test_queries.py all                    # 测试所有查询")
        print("  python test_queries.py weather               # 测试天气查询")
        print("  python test_queries.py light                 # 测试灯光查询")
        print("  python test_queries.py curtain               # 测试窗帘查询")
        print("  python test_queries.py ac                    # 测试空调查询")
        print("  python test_queries.py scene                 # 测试情景模式")
        print("  python test_queries.py \"打开主卧的灯\"        # 测试单个查询")
        print()

        # 测试前10个查询作为示例
        sample_queries = TEST_QUERIES[:10]
        results = []
        for i, query in enumerate(sample_queries, 1):
            print(f"\n[{i}/{len(sample_queries)}]", end="")
            success, response, request_time = test_single_query(query)
            results.append({
                "query": query,
                "success": success,
                "response": response,
                "request_time": request_time
            })
            if i < len(sample_queries):
                time.sleep(1)

        # 将示例测试结果写入文件
        write_results_to_file(results, "test_sample_queries.txt")
