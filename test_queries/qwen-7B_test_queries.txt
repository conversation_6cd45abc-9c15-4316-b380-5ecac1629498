智能家居语音指令测试结果
生成时间: 2025-08-04 18:03:58
总测试数量: 358
================================================================================

[1] 查询: 北京的天气怎么样
状态: ✅ 成功
请求耗时: 22.88秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.930706262588501
  }
}
------------------------------------------------------------

[2] 查询: 深圳的天气怎么样
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.4347105026245117
  }
}
------------------------------------------------------------

[3] 查询: 顺德的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"顺德\"}}"
    ],
    "inference_time": 0.3713068962097168
  }
}
------------------------------------------------------------

[4] 查询: 佛山的天气怎么样
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"room\": \"佛山\"}}"
    ],
    "inference_time": 0.41380834579467773
  }
}
------------------------------------------------------------

[5] 查询: 广东的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"广东\"}}"
    ],
    "inference_time": 0.3700220584869385
  }
}
------------------------------------------------------------

[6] 查询: 广州市南沙区的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"广州市\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.3715517520904541
  }
}
------------------------------------------------------------

[7] 查询: 今天天气怎么样
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.3283064365386963
  }
}
------------------------------------------------------------

[8] 查询: 明天天气怎么样
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.32943105697631836
  }
}
------------------------------------------------------------

[9] 查询: 后天天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+2\"}}"
    ],
    "inference_time": 0.3734579086303711
  }
}
------------------------------------------------------------

[10] 查询: 未来一周天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+1\"}}"
    ],
    "inference_time": 0.3693246841430664
  }
}
------------------------------------------------------------

[11] 查询: 未来两周天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+2\"}}"
    ],
    "inference_time": 0.37039875984191895
  }
}
------------------------------------------------------------

[12] 查询: 过去一周天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.37103700637817383
  }
}
------------------------------------------------------------

[13] 查询: 过去两周天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+2\"}}"
    ],
    "inference_time": 0.37233543395996094
  }
}
------------------------------------------------------------

[14] 查询: 深圳今天的天气怎么样
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.41520142555236816
  }
}
------------------------------------------------------------

[15] 查询: 深圳明天的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"明天\"}}"
    ],
    "inference_time": 0.37276196479797363
  }
}
------------------------------------------------------------

[16] 查询: 深圳后天的天气怎么样
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"后天\"}}"
    ],
    "inference_time": 0.38018250465393066
  }
}
------------------------------------------------------------

[17] 查询: 广东今天的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"广东\"}}"
    ],
    "inference_time": 0.3734009265899658
  }
}
------------------------------------------------------------

[18] 查询: 深圳市南山区今天的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.37316393852233887
  }
}
------------------------------------------------------------

[19] 查询: 今天的温度
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询温度\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.3258020877838135
  }
}
------------------------------------------------------------

[20] 查询: 今天的日出时间
状态: ✅ 成功
请求耗时: 0.35秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询日出时间\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.3394770622253418
  }
}
------------------------------------------------------------

[21] 查询: 今天的日落时间
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.33251094818115234
  }
}
------------------------------------------------------------

[22] 查询: 今天的空气湿度
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询空气湿度\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.3728640079498291
  }
}
------------------------------------------------------------

[23] 查询: 今天的空气质量
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询空气质量\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"\"}}"
    ],
    "inference_time": 0.36461877822875977
  }
}
------------------------------------------------------------

[24] 查询: 今天的风速
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询风速\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.33145689964294434
  }
}
------------------------------------------------------------

[25] 查询: 今天的限行信息
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询限行\", \"arguments\": {\"intent\": \"查询限行\", \"domain\": \"限行\", \"room\": \"伦敦\", \"device\": \"\", \"scene\": \"\", \"pos\": \"伦敦\", \"unit\": \"pos\"}}"
    ],
    "inference_time": 0.43945860862731934
  }
}
------------------------------------------------------------

[26] 查询: 广州的限行信息
状态: ✅ 成功
请求耗时: 0.49秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询限行\", \"arguments\": {\"intent\": \"查询限行\", \"domain\": \"限行\", \"room\": \"广州\", \"device\": \"\", \"scene\": \"\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.48128366470336914
  }
}
------------------------------------------------------------

[27] 查询: 今天广州的限行信息
状态: ✅ 成功
请求耗时: 0.48秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询限行\", \"arguments\": {\"intent\": \"查询限行\", \"domain\": \"限行\", \"room\": \"伦敦\", \"device\": \"\", \"scene\": \"\", \"pos\": \"伦敦\", \"offset\": \"\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.46911048889160156
  }
}
------------------------------------------------------------

[28] 查询: 化妆指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询化妆指数\", \"arguments\": {\"intent\": \"查询化妆指数\", \"domain\": \"指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.35700368881225586
  }
}
------------------------------------------------------------

[29] 查询: 紫外线指数
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询紫外线指数\", \"arguments\": {\"intent\": \"查询紫外线指数\", \"domain\": \"紫外线\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3557314872741699
  }
}
------------------------------------------------------------

[30] 查询: 感冒指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询感冒指数\", \"arguments\": {\"intent\": \"查询感冒指数\", \"domain\": \"指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.358945369720459
  }
}
------------------------------------------------------------

[31] 查询: 洗车指数
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询洗车指数\", \"arguments\": {\"intent\": \"查询洗车指数\", \"domain\": \"洗车指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.38619542121887207
  }
}
------------------------------------------------------------

[32] 查询: 穿衣指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询穿衣指数\", \"arguments\": {\"intent\": \"查询穿衣指数\", \"domain\": \"指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.35787415504455566
  }
}
------------------------------------------------------------

[33] 查询: 运动指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询运动指数\", \"arguments\": {\"intent\": \"查询运动指数\", \"domain\": \"运动指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.36381101608276367
  }
}
------------------------------------------------------------

[34] 查询: 钓鱼指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询钓鱼指数\", \"arguments\": {\"intent\": \"查询钓鱼指数\", \"domain\": \"钓鱼指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.36221861839294434
  }
}
------------------------------------------------------------

[35] 查询: 交通指数
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询交通指数\", \"arguments\": {\"intent\": \"查询交通指数\", \"domain\": \"交通\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3550758361816406
  }
}
------------------------------------------------------------

[36] 查询: 过敏指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询过敏指数\", \"arguments\": {\"intent\": \"查询过敏指数\", \"domain\": \"过敏指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3623039722442627
  }
}
------------------------------------------------------------

[37] 查询: 打开主卧的筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[38] 查询: 打开主卧的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[39] 查询: 打开明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[40] 查询: 打开筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[41] 查询: 打开全屋的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:全屋;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[42] 查询: 关闭主卧的筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:主卧;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[43] 查询: 关闭主卧的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:主卧;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[44] 查询: 关闭明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[45] 查询: 关闭过道三开
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭通断器\", \"domain\": \"通断器\", \"room\": \"过道\", \"device\": \"三开\"}}"
    ],
    "inference_time": 0.39397549629211426
  }
}
------------------------------------------------------------

[46] 查询: 关闭全屋的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:全屋;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[47] 查询: 主卧的吸顶灯一亮度调高一点
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.40207505226135254
  }
}
------------------------------------------------------------

[48] 查询: 主卧的明装筒灯亮度调高一点
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4203314781188965
  }
}
------------------------------------------------------------

[49] 查询: 主卧的灯亮度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高亮度;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[50] 查询: 主卧的吸顶灯一亮度调到最高
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.43210697174072266
  }
}
------------------------------------------------------------

[51] 查询: 主卧的明装筒灯亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.41880083084106445
  }
}
------------------------------------------------------------

[52] 查询: 主卧的灯亮度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高亮度;domain:灯;room:主卧;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[53] 查询: 调高吸顶灯一的亮度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.3972923755645752
  }
}
------------------------------------------------------------

[54] 查询: 调高明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3912332057952881
  }
}
------------------------------------------------------------

[55] 查询: 调高全屋的明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39973950386047363
  }
}
------------------------------------------------------------

[56] 查询: 调高全屋灯的亮度
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.37009310722351074
  }
}
------------------------------------------------------------

[57] 查询: 吸顶灯一的亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.4190709590911865
  }
}
------------------------------------------------------------

[58] 查询: 明装筒灯的亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4189920425415039
  }
}
------------------------------------------------------------

[59] 查询: 全屋的明装筒灯的亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.41829776763916016
  }
}
------------------------------------------------------------

[60] 查询: 全屋灯的亮度调到最高
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"setHigh\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3847677707672119
  }
}
------------------------------------------------------------

[61] 查询: 主卧的吸顶灯一亮度调低一点
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.39812350273132324
  }
}
------------------------------------------------------------

[62] 查询: 主卧的明装筒灯亮度调低一点
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3922436237335205
  }
}
------------------------------------------------------------

[63] 查询: 主卧的灯亮度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低亮度;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[64] 查询: 主卧的吸顶灯一亮度调到最低
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.4004323482513428
  }
}
------------------------------------------------------------

[65] 查询: 主卧的明装筒灯亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39342331886291504
  }
}
------------------------------------------------------------

[66] 查询: 主卧的灯亮度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低亮度;domain:灯;room:主卧;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[67] 查询: 调低吸顶灯一的亮度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.39963436126708984
  }
}
------------------------------------------------------------

[68] 查询: 调低明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3898909091949463
  }
}
------------------------------------------------------------

[69] 查询: 调低全屋明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39795351028442383
  }
}
------------------------------------------------------------

[70] 查询: 调低全屋灯的亮度
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.36947035789489746
  }
}
------------------------------------------------------------

[71] 查询: 吸顶灯一的亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.38907861709594727
  }
}
------------------------------------------------------------

[72] 查询: 明装筒灯的亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39225172996520996
  }
}
------------------------------------------------------------

[73] 查询: 全屋明装筒灯的亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39106082916259766
  }
}
------------------------------------------------------------

[74] 查询: 全屋灯的亮度调到最低
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3639240264892578
  }
}
------------------------------------------------------------

[75] 查询: 主卧的明装筒灯一亮度设成80%
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.41257143020629883
  }
}
------------------------------------------------------------

[76] 查询: 主卧的明装筒灯亮度设成80%
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4135441780090332
  }
}
------------------------------------------------------------

[77] 查询: 主卧的灯亮度亮度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3858370780944824
  }
}
------------------------------------------------------------

[78] 查询: 明装筒灯一亮度设成80%
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3885536193847656
  }
}
------------------------------------------------------------

[79] 查询: 明装筒灯亮度为80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3850443363189697
  }
}
------------------------------------------------------------

[80] 查询: 全屋的明装筒灯亮度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3851149082183838
  }
}
------------------------------------------------------------

[81] 查询: 全屋灯的亮度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.38442277908325195
  }
}
------------------------------------------------------------

[82] 查询: 主卧的明装筒灯一色温调高一点
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"%\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.43153905868530273
  }
}
------------------------------------------------------------

[83] 查询: 主卧的明装筒灯色温调高一点
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.425062894821167
  }
}
------------------------------------------------------------

[84] 查询: 主卧的明装筒灯一色温调到最高
状态: ✅ 成功
请求耗时: 0.47秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100%\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.4587130546569824
  }
}
------------------------------------------------------------

[85] 查询: 主卧的明装筒灯色温调到最高
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.44687628746032715
  }
}
------------------------------------------------------------

[86] 查询: 主卧色温调高一点
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.40593695640563965
  }
}
------------------------------------------------------------

[87] 查询: 明装筒灯一的色温调高一点
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"5000\", \"room\": \"客厅\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.4396023750305176
  }
}
------------------------------------------------------------

[88] 查询: 主卧色温调到最高
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"色温\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.40503740310668945
  }
}
------------------------------------------------------------

[89] 查询: 明装筒灯一的色温调到最高
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"色温\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.4412989616394043
  }
}
------------------------------------------------------------

[90] 查询: 明装筒灯的色温调高一点
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.42421913146972656
  }
}
------------------------------------------------------------

[91] 查询: 调高全屋明装筒灯的色温
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.40532660484313965
  }
}
------------------------------------------------------------

[92] 查询: 明装筒灯的色温调到最高
状态: ✅ 成功
请求耗时: 0.52秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"setHigh\", \"room\": \"默认\", \"device\": \"明装筒灯\", \"scene\": \"\", \"pos\": \"\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.5079882144927979
  }
}
------------------------------------------------------------

[93] 查询: 全屋明装筒灯的色温调到最高
状态: ✅ 成功
请求耗时: 0.48秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"10000\", \"room\": \"默认\", \"device\": \"明装筒灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.4693315029144287
  }
}
------------------------------------------------------------

[94] 查询: 调高全屋灯的色温
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.37639498710632324
  }
}
------------------------------------------------------------

[95] 查询: 主卧的明装筒灯一色温调低一点
状态: ✅ 成功
请求耗时: 0.55秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.5436513423919678
  }
}
------------------------------------------------------------

[96] 查询: 主卧的明装筒灯色温调低一点
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"unit\": \"色温\"}}"
    ],
    "inference_time": 0.44754838943481445
  }
}
------------------------------------------------------------

[97] 查询: 主卧的明装筒灯一色温调到最低
状态: ✅ 成功
请求耗时: 0.48秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"0\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.46639132499694824
  }
}
------------------------------------------------------------

[98] 查询: 主卧的明装筒灯色温调到最低
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColor温\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"最低\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.40535569190979004
  }
}
------------------------------------------------------------

[99] 查询: 主卧色温调低一点
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"默认\"}}"
    ],
    "inference_time": 0.38324499130249023
  }
}
------------------------------------------------------------

[100] 查询: 明装筒灯一的色温调低一点
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.40638065338134766
  }
}
------------------------------------------------------------

[101] 查询: 主卧色温调到最低
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"色温设定器\"}}"
    ],
    "inference_time": 0.4049654006958008
  }
}
------------------------------------------------------------

[102] 查询: 明装筒灯一的色温调到最低
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"最低\", \"room\": \"默认\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.41108274459838867
  }
}
------------------------------------------------------------

[103] 查询: 明装筒灯的色温调低一点
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3975212574005127
  }
}
------------------------------------------------------------

[104] 查询: 调低全屋明装筒灯的色温
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.40617871284484863
  }
}
------------------------------------------------------------

[105] 查询: 调低全屋灯的色温
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.37581634521484375
  }
}
------------------------------------------------------------

[106] 查询: 全屋明装筒灯的色温调到最低
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"最低\", \"room\": \"默认\", \"device\": \"明装筒灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.4373660087585449
  }
}
------------------------------------------------------------

[107] 查询: 全屋灯的色温调到最低
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.378389835357666
  }
}
------------------------------------------------------------

[108] 查询: 主卧的明装筒灯一色温设成3700
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4181976318359375
  }
}
------------------------------------------------------------

[109] 查询: 主卧的明装筒灯色温设成3700
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColor温\", \"arguments\": {\"intent\": \"调色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4191887378692627
  }
}
------------------------------------------------------------

[110] 查询: 主卧色温设成3700
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"色温\", \"value\": \"3700\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.41228580474853516
  }
}
------------------------------------------------------------

[111] 查询: 明装筒灯一的色温设成3700
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"客厅\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.42626357078552246
  }
}
------------------------------------------------------------

[112] 查询: 明装筒灯的色温设成3700
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.41973161697387695
  }
}
------------------------------------------------------------

[113] 查询: 全屋明装筒灯的色温设成3700
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3917062282562256
  }
}
------------------------------------------------------------

[114] 查询: 全屋灯的色温设成3700
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3980691432952881
  }
}
------------------------------------------------------------

[115] 查询: 打开主卧的一路通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:主卧;device:一路通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[116] 查询: 打开主卧的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[117] 查询: 打开通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[118] 查询: 打开通断器一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;device:通断器一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[119] 查询: 打开全屋的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[120] 查询: 打开6路通断器灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:6路通断器灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[121] 查询: 打开海上的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:海上;device:海上的灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[122] 查询: 关闭主卧的一路通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:主卧;device:一路通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[123] 查询: 关闭主卧的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[124] 查询: 关闭通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[125] 查询: 关闭通断器一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;device:通断器一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[126] 查询: 关闭全屋的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[127] 查询: 关闭6路通断器灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;device:6路通断器灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[128] 查询: 打开主卧的纱帘
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧纱帘\"}}"
    ],
    "inference_time": 0.3718147277832031
  }
}
------------------------------------------------------------

[129] 查询: 打开主卧的布帘
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧布帘\"}}"
    ],
    "inference_time": 0.36402249336242676
  }
}
------------------------------------------------------------

[130] 查询: 打开主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[131] 查询: 打开窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[132] 查询: 打开纱帘
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.32601356506347656
  }
}
------------------------------------------------------------

[133] 查询: 打开布帘
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.32105016708374023
  }
}
------------------------------------------------------------

[134] 查询: 打开窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[135] 查询: 打开全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[136] 查询: 关闭主卧的纱帘
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧纱帘\"}}"
    ],
    "inference_time": 0.35634636878967285
  }
}
------------------------------------------------------------

[137] 查询: 关闭主卧的布帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧布帘\"}}"
    ],
    "inference_time": 0.34856319427490234
  }
}
------------------------------------------------------------

[138] 查询: 关闭主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[139] 查询: 关闭窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[140] 查询: 关闭纱帘
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.329456090927124
  }
}
------------------------------------------------------------

[141] 查询: 关闭布帘
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3225712776184082
  }
}
------------------------------------------------------------

[142] 查询: 关上窗帘
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3215827941894531
  }
}
------------------------------------------------------------

[143] 查询: 关闭全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[144] 查询: 暂停主卧的纱帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"暂停窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧纱帘\"}}"
    ],
    "inference_time": 0.3569469451904297
  }
}
------------------------------------------------------------

[145] 查询: 暂停主卧的布帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"暂停窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧布帘\"}}"
    ],
    "inference_time": 0.3485124111175537
  }
}
------------------------------------------------------------

[146] 查询: 暂停主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[147] 查询: 暂停窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[148] 查询: 暂停纱帘
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3277127742767334
  }
}
------------------------------------------------------------

[149] 查询: 暂停布帘
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"暂停窗帘\", \"domain\": \"窗帘\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3228111267089844
  }
}
------------------------------------------------------------

[150] 查询: 暂停窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[151] 查询: 暂停全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[152] 查询: 主卧的纱帘开合度设成80%
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"卧室纱帘\", \"value\": \"80\"}}"
    ],
    "inference_time": 0.3966493606567383
  }
}
------------------------------------------------------------

[153] 查询: 主卧的布帘开合度设成80%
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"布帘\", \"value\": \"80\"}}"
    ],
    "inference_time": 0.38567590713500977
  }
}
------------------------------------------------------------

[154] 查询: 主卧的窗帘电机开合度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"窗帘电机\", \"value\": \"80\"}}"
    ],
    "inference_time": 0.3858225345611572
  }
}
------------------------------------------------------------

[155] 查询: 主卧的开合度设成80%
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"通断器\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"主卧通断器\"}}"
    ],
    "inference_time": 0.44486427307128906
  }
}
------------------------------------------------------------

[156] 查询: 窗帘电机开合度设成80%
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"窗帘\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.39733052253723145
  }
}
------------------------------------------------------------

[157] 查询: 纱帘开合度设成80%
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"窗帘\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3979837894439697
  }
}
------------------------------------------------------------

[158] 查询: 布帘开合度设成80%
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"窗帘\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.40046262741088867
  }
}
------------------------------------------------------------

[159] 查询: 全屋的开合度设成80%
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"通断器\", \"value\": \"80\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.42009496688842773
  }
}
------------------------------------------------------------

[160] 查询: 打开主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[161] 查询: 打开主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[162] 查询: 打开窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[163] 查询: 打开窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[164] 查询: 打开全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[165] 查询: 打开窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[166] 查询: 关闭主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[167] 查询: 关闭主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[168] 查询: 关闭窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[169] 查询: 关闭窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[170] 查询: 关闭全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[171] 查询: 关闭窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[172] 查询: 暂停主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[173] 查询: 暂停主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[174] 查询: 暂停窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[175] 查询: 暂停窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[176] 查询: 暂停全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[177] 查询: 暂停窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[178] 查询: 回家模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[179] 查询: 到家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[180] 查询: 回来了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[181] 查询: 回家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[182] 查询: 离家模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[183] 查询: 我走了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[184] 查询: 出门
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[185] 查询: 上班去了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[186] 查询: 外出
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[187] 查询: 我离家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[188] 查询: 旅游
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询天气状况\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.38765907287597656
  }
}
------------------------------------------------------------

[189] 查询: 用餐模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:用餐模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[190] 查询: 我要吃饭了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:用餐模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[191] 查询: 会客模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:会客模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[192] 查询: 有客人来了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:会客模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[193] 查询: 来客人了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:会客模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[194] 查询: 晨起模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:晨起模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[195] 查询: 我要起床了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:晨起模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[196] 查询: 我醒了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:晨起模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[197] 查询: 睡眠模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:睡眠模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[198] 查询: 我要睡觉了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:睡眠模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[199] 查询: 执行全开模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:全开模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[200] 查询: 执行全关模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:全关模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[201] 查询: 打开主卧的空调一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧;device:空调一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[202] 查询: 打开主卧的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[203] 查询: 打开主卧的主卧空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧;device:主卧空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[204] 查询: 打开主卧空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧;device:主卧空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[205] 查询: 打开客厅东空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:客厅;device:客厅东空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[206] 查询: 打开客厅西空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:客厅;device:客厅西空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[207] 查询: 打开空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[208] 查询: 打开全家的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[209] 查询: 关闭主卧的空调一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调;room:主卧;device:空调一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[210] 查询: 关闭主卧的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[211] 查询: 关闭空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[212] 查询: 关闭全家的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[213] 查询: 主卧的空调一的温度设成26度
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"设置温度\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"卧室\", \"device\": \"主卧空调一\"}}"
    ],
    "inference_time": 0.44661808013916016
  }
}
------------------------------------------------------------

[214] 查询: 主卧的空调温度设成26度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"卧室\", \"device\": \"主卧空调\"}}"
    ],
    "inference_time": 0.401151180267334
  }
}
------------------------------------------------------------

[215] 查询: 空调温度设成26度
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.36438465118408203
  }
}
------------------------------------------------------------

[216] 查询: 空调一的温度设成26度
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"空调1\"}}"
    ],
    "inference_time": 0.37848758697509766
  }
}
------------------------------------------------------------

[217] 查询: 全家的空调温度设成26度
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26度\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3774898052215576
  }
}
------------------------------------------------------------

[218] 查询: 主卧的主卧空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"value\": \"高\", \"room\": \"卧室\", \"device\": \"主卧空调\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.42764735221862793
  }
}
------------------------------------------------------------

[219] 查询: 主卧的空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[220] 查询: 空调一的温度调高一点
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.3839287757873535
  }
}
------------------------------------------------------------

[221] 查询: 空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[222] 查询: 全家的空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[223] 查询: 主卧的主卧空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"max\", \"room\": \"卧室\", \"device\": \"主卧空调\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.42345476150512695
  }
}
------------------------------------------------------------

[224] 查询: 主卧的空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:主卧;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[225] 查询: 空调一的温度调到最高
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"max\", \"room\": \"客厅\", \"device\": \"空调1\"}}"
    ],
    "inference_time": 0.37586045265197754
  }
}
------------------------------------------------------------

[226] 查询: 空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[227] 查询: 全家的空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:all;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[228] 查询: 主卧的主卧空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"调低温度\", \"domain\": \"空调\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧空调\"}}"
    ],
    "inference_time": 0.37706804275512695
  }
}
------------------------------------------------------------

[229] 查询: 主卧的空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[230] 查询: 空调一的温度调低一点
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"调低温度\", \"domain\": \"空调\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.36751341819763184
  }
}
------------------------------------------------------------

[231] 查询: 空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[232] 查询: 全家的空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[233] 查询: 主卧的主卧空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"最低\", \"room\": \"卧室\", \"device\": \"主卧空调\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.42452120780944824
  }
}
------------------------------------------------------------

[234] 查询: 主卧的空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:主卧;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[235] 查询: 空调一的温度调到最低
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"最低\", \"room\": \"客厅\", \"device\": \"空调1\"}}"
    ],
    "inference_time": 0.37520313262939453
  }
}
------------------------------------------------------------

[236] 查询: 空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[237] 查询: 全家的空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:all;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[238] 查询: 空调一的工作模式设成制冷模式
状态: ✅ 成功
请求耗时: 0.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制冷模式\"}}"
    ],
    "inference_time": 0.286299467086792
  }
}
------------------------------------------------------------

[239] 查询: 空调一的工作模式设成制热模式
状态: ✅ 成功
请求耗时: 0.32秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.31259870529174805
  }
}
------------------------------------------------------------

[240] 查询: 空调一的工作模式设成送风模式
状态: ✅ 成功
请求耗时: 0.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"送风模式\"}}"
    ],
    "inference_time": 0.288008451461792
  }
}
------------------------------------------------------------

[241] 查询: 空调一的工作模式设成换气模式
状态: ✅ 成功
请求耗时: 0.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"换气模式\"}}"
    ],
    "inference_time": 0.28691554069519043
  }
}
------------------------------------------------------------

[242] 查询: 空调一的工作模式设成除湿模式
状态: ✅ 成功
请求耗时: 0.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"除湿模式\"}}"
    ],
    "inference_time": 0.28980231285095215
  }
}
------------------------------------------------------------

[243] 查询: 主卧的主卧空调的模式设成制冷模式
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制冷模式\", \"room\": \"卧室\", \"device\": \"主卧空调\"}}"
    ],
    "inference_time": 0.37755846977233887
  }
}
------------------------------------------------------------

[244] 查询: 主卧的空调设成制冷模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置模式;domain:空调;room:主卧;value:制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[245] 查询: 空调设成制冷模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置模式;domain:空调;value:制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[246] 查询: 空调一设成制冷模式
状态: ✅ 成功
请求耗时: 0.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制冷模式\"}}"
    ],
    "inference_time": 0.2866799831390381
  }
}
------------------------------------------------------------

[247] 查询: 全家的空调设成制冷模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置模式;domain:空调;room:all;value:制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[248] 查询: 空调一的风速设成高速
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"风速\", \"value\": \"高速\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.4080655574798584
  }
}
------------------------------------------------------------

[249] 查询: 空调一的风速设成中速
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setWindSpeed\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"风速\", \"value\": \"中\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.39826321601867676
  }
}
------------------------------------------------------------

[250] 查询: 空调一的风速设成低速
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setWindSpeed\", \"arguments\": {\"intent\": \"调低风速\", \"domain\": \"风速\", \"value\": \"低\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.3963003158569336
  }
}
------------------------------------------------------------

[251] 查询: 空调一的风速设成自动
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setWindSpeed\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"风速\", \"value\": \"自动\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.39829301834106445
  }
}
------------------------------------------------------------

[252] 查询: 主卧的主卧空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"风速\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧空调\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.43227529525756836
  }
}
------------------------------------------------------------

[253] 查询: 主卧的空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:空调;room:主卧;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[254] 查询: 空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:空调;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[255] 查询: 空调一的风速设成自动
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setWindSpeed\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"风速\", \"value\": \"自动\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.4027879238128662
  }
}
------------------------------------------------------------

[256] 查询: 全家的空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:空调;room:all;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[257] 查询: 打开次卧的开关一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;room:次卧;device:开关一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[258] 查询: 打开次卧的开关
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开开关\", \"domain\": \"开关\", \"room\": \"次卧\", \"device\": \"\"}}"
    ],
    "inference_time": 0.33333611488342285
  }
}
------------------------------------------------------------

[259] 查询: 打开客厅的过道三开
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开开关\", \"domain\": \"开关\", \"room\": \"客厅\", \"device\": \"过道三开\"}}"
    ],
    "inference_time": 0.3491654396057129
  }
}
------------------------------------------------------------

[260] 查询: 打开客厅的餐厅筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:客厅;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[261] 查询: 打开客厅的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:客厅;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[262] 查询: 打开客厅的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:客厅"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[263] 查询: 打开开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[264] 查询: 打开开关一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;device:开关一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[265] 查询: 打开全家的开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[266] 查询: 关闭主卧的主卧双开
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭开关\", \"domain\": \"开关\", \"room\": \"卧室\", \"device\": \"主卧双开\"}}"
    ],
    "inference_time": 0.38114047050476074
  }
}
------------------------------------------------------------

[267] 查询: 关闭主卧的开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[268] 查询: 关闭开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[269] 查询: 关闭开关一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;device:开关一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[270] 查询: 关闭全家的开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[271] 查询: 打开书房的开关三
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;room:书房;device:开关三"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[272] 查询: 打开"餐厅筒灯"
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:餐厅;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[273] 查询: 关闭书房的"餐厅筒灯"
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:书房;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[274] 查询: 关闭客厅的餐厅筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:客厅;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[275] 查询: 关闭客厅的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:客厅;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[276] 查询: 关闭客厅的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:客厅"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[277] 查询: 关闭开关三
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;device:开关三"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[278] 查询: 打开主卧的插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;room:主卧;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[279] 查询: 打开主卧的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[280] 查询: 打开插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[281] 查询: 打开插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[282] 查询: 打开全家的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[283] 查询: 关闭主卧的插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;room:主卧;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[284] 查询: 关闭主卧的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[285] 查询: 关闭插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[286] 查询: 关闭插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[287] 查询: 关闭全家的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[288] 查询: 打开主卧的地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;room:主卧;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[289] 查询: 打开主卧的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[290] 查询: 打开地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[291] 查询: 打开地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[292] 查询: 打开全家的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[293] 查询: 关闭主卧的地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;room:主卧;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[294] 查询: 关闭主卧的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[295] 查询: 关闭地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[296] 查询: 关闭地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[297] 查询: 关闭全家的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[298] 查询: 打开主卧的新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[299] 查询: 打开新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[300] 查询: 打开新风一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;device:新风一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[301] 查询: 打开全家的新风机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;room:all;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[302] 查询: 关闭主卧的新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[303] 查询: 关闭新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[304] 查询: 关闭新风一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;device:新风一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[305] 查询: 关闭全家的新风机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;room:all;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[306] 查询: 主卧新风的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[307] 查询: 主卧新风的风速设成高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:高"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[308] 查询: 主卧新风的风速设成中
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:中"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[309] 查询: 主卧新风的风速设成低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:低"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[310] 查询: 新风机风速调高
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"新风\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.4292106628417969
  }
}
------------------------------------------------------------

[311] 查询: 新风机风速调低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低风速\", \"domain\": \"新风\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"新风机\"}}"
    ],
    "inference_time": 0.3918745517730713
  }
}
------------------------------------------------------------

[312] 查询: 新风机打开杀菌
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[313] 查询: 新风机关闭杀菌
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[314] 查询: 打开安防
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开安防;domain:安防"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[315] 查询: 关闭安防
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭安防;domain:安防"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[316] 查询: 小零小零
状态: ✅ 成功
请求耗时: 0.59秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"content\": \"您好！很高兴为您服务，请问需要控制哪些设备呢？\", \"domain\": \"闲聊\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\", \"scene\": \"\", \"pos\": \"\", \"offset\": \"\", \"unit\": \"\"}}"
    ],
    "inference_time": 0.5825178623199463
  }
}
------------------------------------------------------------

[317] 查询: 查询天气
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.33004307746887207
  }
}
------------------------------------------------------------

[318] 查询: 关闭全家的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[319] 查询: 查询我的设备数量
状态: ✅ 成功
请求耗时: 0.47秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\", \"scene\": \"\", \"pos\": \"\", \"offset\": \"\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.46197986602783203
  }
}
------------------------------------------------------------

[320] 查询: 退下吧
状态: ✅ 成功
请求耗时: 0.27秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"dialog\", \"arguments\": {\"intent\": \"终止对话\", \"value\": \"\", \"domain\": \"对话\"}}"
    ],
    "inference_time": 0.26166868209838867
  }
}
------------------------------------------------------------

[321] 查询: 重新开始
状态: ✅ 成功
请求耗时: 0.27秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"dialog\", \"arguments\": {\"intent\": \"重新开始\", \"value\": \"\", \"domain\": \"对话\"}}"
    ],
    "inference_time": 0.2618873119354248
  }
}
------------------------------------------------------------

[322] 查询: 介绍几个景点给我
状态: ✅ 成功
请求耗时: 0.52秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"我为您推荐几个热门景点：长城、故宫、西湖、埃及金字塔、巴黎埃菲尔铁塔。\", \"offset\": \"+0\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.5098714828491211
  }
}
------------------------------------------------------------

[323] 查询: 你是谁
状态: ✅ 成功
请求耗时: 0.22秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "我是小灵，您的智能家居助手，可以帮您控制家中的各种设备。请问需要我帮您做什么？"
    ],
    "inference_time": 0.21320438385009766
  }
}
------------------------------------------------------------

[324] 查询: 油烟机风速调大一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[325] 查询: 电饭煲开始煮饭
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[326] 查询: 微波炉剩余烹饪时长
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[327] 查询: 洗衣机调到快速洗
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[328] 查询: 新风机设为睡眠模式
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\", \"room\": \"客厅\", \"device\": \"新风机\"}}"
    ],
    "inference_time": 0.3782613277435303
  }
}
------------------------------------------------------------

[329] 查询: 关闭卧室所有灯
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3231637477874756
  }
}
------------------------------------------------------------

[330] 查询: 按摩椅开始按摩
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[331] 查询: 打开加湿器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[332] 查询: 打开投影
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[333] 查询: 灯亮度和色温调低一点
状态: ✅ 成功
请求耗时: 0.67秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.6648516654968262
  }
}
------------------------------------------------------------

[334] 查询: 灯亮度和色温调高一点
状态: ✅ 成功
请求耗时: 0.67秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.6597445011138916
  }
}
------------------------------------------------------------

[335] 查询: 灯亮度调到80%色温调到3700k
状态: ✅ 成功
请求耗时: 0.70秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调色温\", \"domain\": \"色温\", \"value\": \"3700\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.6874117851257324
  }
}
------------------------------------------------------------

[336] 查询: 打开主卧的灯然后亮度和色温调低一点
状态: ✅ 成功
请求耗时: 1.16秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"主卧灯\", \"value\": \"\"}}, {\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧灯\", \"unit\": \"day\", \"offset\": \"+0\"}, {\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧灯\", \"unit\": \"day\", \"offset\": \"+0\"}}]}"
    ],
    "inference_time": 1.1521899700164795
  }
}
------------------------------------------------------------

[337] 查询: 打开主卧的灯然后亮度和色温调高一点
状态: ✅ 成功
请求耗时: 1.11秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"主卧灯\", \"value\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧灯\"}, \"offset\": \"+1\", \"unit\": \"day\"}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧灯\"}}"
    ],
    "inference_time": 1.1032640933990479
  }
}
------------------------------------------------------------

[338] 查询: 打开主卧的灯然后亮度调到80%色温调到3700k
状态: ✅ 成功
请求耗时: 0.71秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"主卧灯\", \"value\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"主卧灯\"}}"
    ],
    "inference_time": 0.6953761577606201
  }
}
------------------------------------------------------------

[339] 查询: 打开全家的灯并把亮度调到80%色温调到3700k
状态: ✅ 成功
请求耗时: 0.72秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"全屋\", \"device\": \"\"}}, {\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"全屋\", \"device\": \"\"}, \"offset\": \"+0\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.7157883644104004
  }
}
------------------------------------------------------------

[340] 查询: 空调的温度调低一点并调成制冷
状态: ✅ 成功
请求耗时: 0.72秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"调低温度\", \"domain\": \"空调\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"客厅空调\", \"unit\": \"day\"}}",
      "{\"name\": \"setMode\", \"arguments\": {\"intent\": \"设置模式\", \"domain\": \"模式\", \"value\": \"制冷\", \"room\": \"客厅\", \"device\": \"客厅空调\"}}"
    ],
    "inference_time": 0.7093033790588379
  }
}
------------------------------------------------------------

[341] 查询: 空调的温度调到26度模式调成制热
状态: ✅ 成功
请求耗时: 0.69秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26度\", \"device\": \"\", \"scene\": \"\", \"room\": \"客厅\", \"unit\": \"day\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.6773321628570557
  }
}
------------------------------------------------------------

[342] 查询: 空调的温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 0.58秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.5723698139190674
  }
}
------------------------------------------------------------

[343] 查询: 客厅空调的温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 0.58秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.5716736316680908
  }
}
------------------------------------------------------------

[344] 查询: 打开客厅的空调然后温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 0.84秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"温度\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.8313684463500977
  }
}
------------------------------------------------------------

[345] 查询: 打开全家的空调然后温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 0.87秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"全屋\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"温度\", \"value\": \"26\", \"room\": \"全屋\", \"device\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.8597142696380615
  }
}
------------------------------------------------------------

[346] 查询: 打开灯和空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调",
      "intent:打开灯;domain:灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[347] 查询: 打开灯和地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖",
      "intent:打开灯;domain:灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[348] 查询: 打开灯和新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯",
      "intent:打开新风;domain:新风"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[349] 查询: 打开空调和新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调",
      "intent:打开新风;domain:新风"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[350] 查询: 打开主卧的灯和空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧",
      "intent:打开空调;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[351] 查询: 打开全家的灯和空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:all",
      "intent:打开空调;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[352] 查询: 打开灯和空调还有窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘",
      "intent:打开空调;domain:空调",
      "intent:打开灯;domain:灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[353] 查询: 打开主卧的灯和空调还有窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧",
      "intent:打开空调;domain:空调;room:主卧",
      "intent:打开窗帘;domain:窗帘;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[354] 查询: 打开全家的灯和空调还有窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:all",
      "intent:打开空调;domain:空调;room:all",
      "intent:打开窗帘;domain:窗帘;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[355] 查询: 打开主卧的所有设备
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开设备\", \"domain\": \"设备\", \"room\": \"卧室\", \"device\": \"\", \"value\": \"主卧灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.4423179626464844
  }
}
------------------------------------------------------------

[356] 查询: 打开氛围灯然后亮度调低色温调低
状态: ✅ 成功
请求耗时: 0.76秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"氛围灯\", \"value\": \"\"}}, {\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"\"}, \"offset\": \"+1\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.747565507888794
  }
}
------------------------------------------------------------

[357] 查询: 打开氛围灯然后亮度调到50色温调到4000
状态: ✅ 成功
请求耗时: 0.64秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"氛围灯\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调亮度\", \"domain\": \"灯\", \"value\": \"50\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.634676456451416
  }
}
------------------------------------------------------------

[358] 查询: 打开空调一工作模式调成制热温度调到26度风速调成高速
状态: ✅ 成功
请求耗时: 0.62秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"客厅空调\", \"value\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\", \"value\": \"\"}}"
    ],
    "inference_time": 0.6073715686798096
  }
}
------------------------------------------------------------

