智能家居语音指令测试结果
生成时间: 2025-08-04 15:11:47
总测试数量: 358
================================================================================

[1] 查询: 北京的天气怎么样
状态: ✅ 成功
请求耗时: 0.49秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.4815690517425537
  }
}
------------------------------------------------------------

[2] 查询: 深圳的天气怎么样
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.44370436668395996
  }
}
------------------------------------------------------------

[3] 查询: 顺德的天气怎么样
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"顺德\"}}"
    ],
    "inference_time": 0.3972468376159668
  }
}
------------------------------------------------------------

[4] 查询: 佛山的天气怎么样
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\"}}"
    ],
    "inference_time": 0.397228479385376
  }
}
------------------------------------------------------------

[5] 查询: 广东的天气怎么样
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"广东\"}}"
    ],
    "inference_time": 0.3951396942138672
  }
}
------------------------------------------------------------

[6] 查询: 广州市南沙区的天气怎么样
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"广州市南沙区\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.3486521244049072
  }
}
------------------------------------------------------------

[7] 查询: 今天天气怎么样
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.33119916915893555
  }
}
------------------------------------------------------------

[8] 查询: 明天天气怎么样
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.33019018173217773
  }
}
------------------------------------------------------------

[9] 查询: 后天天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+2\"}}"
    ],
    "inference_time": 0.3739182949066162
  }
}
------------------------------------------------------------

[10] 查询: 未来一周天气怎么样
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.3878607749938965
  }
}
------------------------------------------------------------

[11] 查询: 未来两周天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+2\"}}"
    ],
    "inference_time": 0.372805118560791
  }
}
------------------------------------------------------------

[12] 查询: 过去一周天气怎么样
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.33288121223449707
  }
}
------------------------------------------------------------

[13] 查询: 过去两周天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+2\"}}"
    ],
    "inference_time": 0.37395381927490234
  }
}
------------------------------------------------------------

[14] 查询: 深圳今天的天气怎么样
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.4160747528076172
  }
}
------------------------------------------------------------

[15] 查询: 深圳明天的天气怎么样
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"明天\"}}"
    ],
    "inference_time": 0.39963746070861816
  }
}
------------------------------------------------------------

[16] 查询: 深圳后天的天气怎么样
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"后天\"}}"
    ],
    "inference_time": 0.3992183208465576
  }
}
------------------------------------------------------------

[17] 查询: 广东今天的天气怎么样
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"广东\"}}"
    ],
    "inference_time": 0.3917217254638672
  }
}
------------------------------------------------------------

[18] 查询: 深圳市南山区今天的天气怎么样
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.391693115234375
  }
}
------------------------------------------------------------

[19] 查询: 今天的温度
状态: ✅ 成功
请求耗时: 0.35秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询温度\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.34331250190734863
  }
}
------------------------------------------------------------

[20] 查询: 今天的日出时间
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询日出时间\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.35830211639404297
  }
}
------------------------------------------------------------

[21] 查询: 今天的日落时间
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.34997010231018066
  }
}
------------------------------------------------------------

[22] 查询: 今天的空气湿度
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询空气湿度\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.3496816158294678
  }
}
------------------------------------------------------------

[23] 查询: 今天的空气质量
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询空气质量\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"\"}}"
    ],
    "inference_time": 0.38407158851623535
  }
}
------------------------------------------------------------

[24] 查询: 今天的风速
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询风速\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.349017858505249
  }
}
------------------------------------------------------------

[25] 查询: 今天的限行信息
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询限行\", \"arguments\": {\"intent\": \"查询限行\", \"domain\": \"限行\", \"room\": \"伦敦\", \"device\": \"\", \"scene\": \"\", \"pos\": \"伦敦\", \"unit\": \"pos\"}}"
    ],
    "inference_time": 0.4537363052368164
  }
}
------------------------------------------------------------

[26] 查询: 广州的限行信息
状态: ✅ 成功
请求耗时: 0.49秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询限行\", \"arguments\": {\"intent\": \"查询限行\", \"domain\": \"限行\", \"room\": \"广州\", \"device\": \"\", \"scene\": \"\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.480330228805542
  }
}
------------------------------------------------------------

[27] 查询: 今天广州的限行信息
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询限行\", \"arguments\": {\"intent\": \"查询限行\", \"domain\": \"限行\", \"room\": \"伦敦\", \"device\": \"\", \"scene\": \"\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.4421389102935791
  }
}
------------------------------------------------------------

[28] 查询: 化妆指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询化妆指数\", \"arguments\": {\"intent\": \"查询化妆指数\", \"domain\": \"指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.35985851287841797
  }
}
------------------------------------------------------------

[29] 查询: 紫外线指数
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询紫外线指数\", \"arguments\": {\"intent\": \"查询紫外线指数\", \"domain\": \"紫外线\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3802602291107178
  }
}
------------------------------------------------------------

[30] 查询: 感冒指数
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询感冒指数\", \"arguments\": {\"intent\": \"查询感冒指数\", \"domain\": \"指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.382082462310791
  }
}
------------------------------------------------------------

[31] 查询: 洗车指数
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询洗车指数\", \"arguments\": {\"intent\": \"查询洗车指数\", \"domain\": \"洗车指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.4037327766418457
  }
}
------------------------------------------------------------

[32] 查询: 穿衣指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询穿衣指数\", \"arguments\": {\"intent\": \"查询穿衣指数\", \"domain\": \"指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3580129146575928
  }
}
------------------------------------------------------------

[33] 查询: 运动指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询运动指数\", \"arguments\": {\"intent\": \"查询运动指数\", \"domain\": \"运动指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3649594783782959
  }
}
------------------------------------------------------------

[34] 查询: 钓鱼指数
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询钓鱼指数\", \"arguments\": {\"intent\": \"查询钓鱼指数\", \"domain\": \"钓鱼指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.38909912109375
  }
}
------------------------------------------------------------

[35] 查询: 交通指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询交通指数\", \"arguments\": {\"intent\": \"查询交通指数\", \"domain\": \"交通\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.35875725746154785
  }
}
------------------------------------------------------------

[36] 查询: 过敏指数
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询过敏指数\", \"arguments\": {\"intent\": \"查询过敏指数\", \"domain\": \"过敏指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3684215545654297
  }
}
------------------------------------------------------------

[37] 查询: 打开主卧的筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[38] 查询: 打开主卧的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[39] 查询: 打开明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[40] 查询: 打开筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[41] 查询: 打开全屋的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:全屋;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[42] 查询: 关闭主卧的筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:主卧;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[43] 查询: 关闭主卧的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:主卧;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[44] 查询: 关闭明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[45] 查询: 关闭过道三开
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭通断器\", \"domain\": \"通断器\", \"room\": \"过道\", \"device\": \"三开\"}}"
    ],
    "inference_time": 0.40016984939575195
  }
}
------------------------------------------------------------

[46] 查询: 关闭全屋的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:全屋;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[47] 查询: 主卧的吸顶灯一亮度调高一点
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.427842378616333
  }
}
------------------------------------------------------------

[48] 查询: 主卧的明装筒灯亮度调高一点
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4433627128601074
  }
}
------------------------------------------------------------

[49] 查询: 主卧的灯亮度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高亮度;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[50] 查询: 主卧的吸顶灯一亮度调到最高
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.4499650001525879
  }
}
------------------------------------------------------------

[51] 查询: 主卧的明装筒灯亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4207572937011719
  }
}
------------------------------------------------------------

[52] 查询: 主卧的灯亮度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高亮度;domain:灯;room:主卧;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[53] 查询: 调高吸顶灯一的亮度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.39694666862487793
  }
}
------------------------------------------------------------

[54] 查询: 调高明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3921031951904297
  }
}
------------------------------------------------------------

[55] 查询: 调高全屋的明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39931440353393555
  }
}
------------------------------------------------------------

[56] 查询: 调高全屋灯的亮度
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.372478723526001
  }
}
------------------------------------------------------------

[57] 查询: 吸顶灯一的亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.4198596477508545
  }
}
------------------------------------------------------------

[58] 查询: 明装筒灯的亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4183182716369629
  }
}
------------------------------------------------------------

[59] 查询: 全屋的明装筒灯的亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.42197585105895996
  }
}
------------------------------------------------------------

[60] 查询: 全屋灯的亮度调到最高
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3653092384338379
  }
}
------------------------------------------------------------

[61] 查询: 主卧的吸顶灯一亮度调低一点
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.39812755584716797
  }
}
------------------------------------------------------------

[62] 查询: 主卧的明装筒灯亮度调低一点
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.393024206161499
  }
}
------------------------------------------------------------

[63] 查询: 主卧的灯亮度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低亮度;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[64] 查询: 主卧的吸顶灯一亮度调到最低
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.4078712463378906
  }
}
------------------------------------------------------------

[65] 查询: 主卧的明装筒灯亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3930630683898926
  }
}
------------------------------------------------------------

[66] 查询: 主卧的灯亮度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低亮度;domain:灯;room:主卧;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[67] 查询: 调低吸顶灯一的亮度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.3989841938018799
  }
}
------------------------------------------------------------

[68] 查询: 调低明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39260363578796387
  }
}
------------------------------------------------------------

[69] 查询: 调低全屋明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4014716148376465
  }
}
------------------------------------------------------------

[70] 查询: 调低全屋灯的亮度
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.37242937088012695
  }
}
------------------------------------------------------------

[71] 查询: 吸顶灯一的亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.3934764862060547
  }
}
------------------------------------------------------------

[72] 查询: 明装筒灯的亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39182329177856445
  }
}
------------------------------------------------------------

[73] 查询: 全屋明装筒灯的亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3950777053833008
  }
}
------------------------------------------------------------

[74] 查询: 全屋灯的亮度调到最低
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.36343908309936523
  }
}
------------------------------------------------------------

[75] 查询: 主卧的明装筒灯一亮度设成80%
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4117093086242676
  }
}
------------------------------------------------------------

[76] 查询: 主卧的明装筒灯亮度设成80%
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.41318392753601074
  }
}
------------------------------------------------------------

[77] 查询: 主卧的灯亮度亮度设成80%
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3867826461791992
  }
}
------------------------------------------------------------

[78] 查询: 明装筒灯一亮度设成80%
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.38707590103149414
  }
}
------------------------------------------------------------

[79] 查询: 明装筒灯亮度为80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.38576221466064453
  }
}
------------------------------------------------------------

[80] 查询: 全屋的明装筒灯亮度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3846862316131592
  }
}
------------------------------------------------------------

[81] 查询: 全屋灯的亮度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.38459181785583496
  }
}
------------------------------------------------------------

[82] 查询: 主卧的明装筒灯一色温调高一点
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"%\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4358384609222412
  }
}
------------------------------------------------------------

[83] 查询: 主卧的明装筒灯色温调高一点
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4285562038421631
  }
}
------------------------------------------------------------

[84] 查询: 主卧的明装筒灯一色温调到最高
状态: ✅ 成功
请求耗时: 0.52秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.5114006996154785
  }
}
------------------------------------------------------------

[85] 查询: 主卧的明装筒灯色温调到最高
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"10000\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4551262855529785
  }
}
------------------------------------------------------------

[86] 查询: 主卧色温调高一点
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.42555809020996094
  }
}
------------------------------------------------------------

[87] 查询: 明装筒灯一的色温调高一点
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"高\", \"room\": \"客厅\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.42031288146972656
  }
}
------------------------------------------------------------

[88] 查询: 主卧色温调到最高
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"色温\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.40729331970214844
  }
}
------------------------------------------------------------

[89] 查询: 明装筒灯一的色温调到最高
状态: ✅ 成功
请求耗时: 0.48秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯一\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.46892261505126953
  }
}
------------------------------------------------------------

[90] 查询: 明装筒灯的色温调高一点
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.42656373977661133
  }
}
------------------------------------------------------------

[91] 查询: 调高全屋明装筒灯的色温
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4077584743499756
  }
}
------------------------------------------------------------

[92] 查询: 明装筒灯的色温调到最高
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"10000\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4460639953613281
  }
}
------------------------------------------------------------

[93] 查询: 全屋明装筒灯的色温调到最高
状态: ✅ 成功
请求耗时: 0.50秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"10000\", \"room\": \"默认\", \"device\": \"明装筒灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.49527978897094727
  }
}
------------------------------------------------------------

[94] 查询: 调高全屋灯的色温
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.4013056755065918
  }
}
------------------------------------------------------------

[95] 查询: 主卧的明装筒灯一色温调低一点
状态: ✅ 成功
请求耗时: 0.55秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.544111967086792
  }
}
------------------------------------------------------------

[96] 查询: 主卧的明装筒灯色温调低一点
状态: ✅ 成功
请求耗时: 0.56秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"pos\": \"伦敦\", \"unit\": \"timeRange\"}}"
    ],
    "inference_time": 0.5503518581390381
  }
}
------------------------------------------------------------

[97] 查询: 主卧的明装筒灯一色温调到最低
状态: ✅ 成功
请求耗时: 0.48秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"0\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.47101473808288574
  }
}
------------------------------------------------------------

[98] 查询: 主卧的明装筒灯色温调到最低
状态: ✅ 成功
请求耗时: 0.48秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"最低\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"unit\": \"色温\"}}"
    ],
    "inference_time": 0.4721081256866455
  }
}
------------------------------------------------------------

[99] 查询: 主卧色温调低一点
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"默认\"}}"
    ],
    "inference_time": 0.3861820697784424
  }
}
------------------------------------------------------------

[100] 查询: 明装筒灯一的色温调低一点
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.40866994857788086
  }
}
------------------------------------------------------------

[101] 查询: 主卧色温调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"色温\"}}"
    ],
    "inference_time": 0.39447021484375
  }
}
------------------------------------------------------------

[102] 查询: 明装筒灯一的色温调到最低
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"最低\", \"room\": \"客厅\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.41446638107299805
  }
}
------------------------------------------------------------

[103] 查询: 明装筒灯的色温调低一点
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4010317325592041
  }
}
------------------------------------------------------------

[104] 查询: 调低全屋明装筒灯的色温
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.40788984298706055
  }
}
------------------------------------------------------------

[105] 查询: 调低全屋灯的色温
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.4049496650695801
  }
}
------------------------------------------------------------

[106] 查询: 全屋明装筒灯的色温调到最低
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"最低\", \"room\": \"默认\", \"device\": \"明装筒灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.4409825801849365
  }
}
------------------------------------------------------------

[107] 查询: 全屋灯的色温调到最低
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.37799739837646484
  }
}
------------------------------------------------------------

[108] 查询: 主卧的明装筒灯一色温设成3700
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColor温\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4211156368255615
  }
}
------------------------------------------------------------

[109] 查询: 主卧的明装筒灯色温设成3700
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColor温\", \"arguments\": {\"intent\": \"调色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.42011070251464844
  }
}
------------------------------------------------------------

[110] 查询: 主卧色温设成3700
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"色温\", \"value\": \"3700\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.41275882720947266
  }
}
------------------------------------------------------------

[111] 查询: 明装筒灯一的色温设成3700
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"客厅\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.42732906341552734
  }
}
------------------------------------------------------------

[112] 查询: 明装筒灯的色温设成3700
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4204106330871582
  }
}
------------------------------------------------------------

[113] 查询: 全屋明装筒灯的色温设成3700
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.39300036430358887
  }
}
------------------------------------------------------------

[114] 查询: 全屋灯的色温设成3700
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.39991331100463867
  }
}
------------------------------------------------------------

[115] 查询: 打开主卧的一路通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:主卧;device:一路通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[116] 查询: 打开主卧的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[117] 查询: 打开通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[118] 查询: 打开通断器一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;device:通断器一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[119] 查询: 打开全屋的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[120] 查询: 打开6路通断器灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:6路通断器灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[121] 查询: 打开海上的灯
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[122] 查询: 关闭主卧的一路通断器
状态: ✅ 成功
请求耗时: 17.72秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:主卧;device:一路通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[123] 查询: 关闭主卧的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[124] 查询: 关闭通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[125] 查询: 关闭通断器一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;device:通断器一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[126] 查询: 关闭全屋的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[127] 查询: 关闭6路通断器灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;device:6路通断器灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[128] 查询: 打开主卧的纱帘
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧纱帘\"}}"
    ],
    "inference_time": 0.39563536643981934
  }
}
------------------------------------------------------------

[129] 查询: 打开主卧的布帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[130] 查询: 打开主卧的窗帘电机
状态: ✅ 成功
请求耗时: 21.52秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[131] 查询: 打开窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[132] 查询: 打开纱帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3514842987060547
  }
}
------------------------------------------------------------

[133] 查询: 打开布帘
状态: ✅ 成功
请求耗时: 0.35秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.345813512802124
  }
}
------------------------------------------------------------

[134] 查询: 打开窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[135] 查询: 打开全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[136] 查询: 关闭主卧的纱帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[137] 查询: 关闭主卧的布帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[138] 查询: 关闭主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[139] 查询: 关闭窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[140] 查询: 关闭纱帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[141] 查询: 关闭布帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[142] 查询: 关上窗帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[143] 查询: 关闭全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[144] 查询: 暂停主卧的纱帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[145] 查询: 暂停主卧的布帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[146] 查询: 暂停主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[147] 查询: 暂停窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[148] 查询: 暂停纱帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[149] 查询: 暂停布帘
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[150] 查询: 暂停窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[151] 查询: 暂停全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[152] 查询: 主卧的纱帘开合度设成80%
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[153] 查询: 主卧的布帘开合度设成80%
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[154] 查询: 主卧的窗帘电机开合度设成80%
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[155] 查询: 主卧的开合度设成80%
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[156] 查询: 窗帘电机开合度设成80%
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[157] 查询: 纱帘开合度设成80%
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[158] 查询: 布帘开合度设成80%
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[159] 查询: 全屋的开合度设成80%
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[160] 查询: 打开主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[161] 查询: 打开主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[162] 查询: 打开窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[163] 查询: 打开窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[164] 查询: 打开全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[165] 查询: 打开窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[166] 查询: 关闭主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[167] 查询: 关闭主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[168] 查询: 关闭窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[169] 查询: 关闭窗帘面板一
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[170] 查询: 关闭全屋的窗帘面板
状态: ✅ 成功
请求耗时: 18.58秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[171] 查询: 关闭窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[172] 查询: 暂停主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[173] 查询: 暂停主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[174] 查询: 暂停窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[175] 查询: 暂停窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[176] 查询: 暂停全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[177] 查询: 暂停窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[178] 查询: 回家模式
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[179] 查询: 到家了
状态: ✅ 成功
请求耗时: 20.91秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[180] 查询: 回来了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[181] 查询: 回家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[182] 查询: 离家模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[183] 查询: 我走了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[184] 查询: 出门
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[185] 查询: 上班去了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[186] 查询: 外出
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[187] 查询: 我离家了
状态: ✅ 成功
请求耗时: 21.93秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[188] 查询: 旅游
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[189] 查询: 用餐模式
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[190] 查询: 我要吃饭了
状态: ❌ 失败
请求耗时: 30.00秒
响应: {
  "code": "ERROR",
  "msg": "请求超时 (30秒)",
  "data": null
}
------------------------------------------------------------

[191] 查询: 会客模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[192] 查询: 有客人来了
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[193] 查询: 来客人了
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[194] 查询: 晨起模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[195] 查询: 我要起床了
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[196] 查询: 我醒了
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[197] 查询: 睡眠模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[198] 查询: 我要睡觉了
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[199] 查询: 执行全开模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[200] 查询: 执行全关模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[201] 查询: 打开主卧的空调一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[202] 查询: 打开主卧的空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[203] 查询: 打开主卧的主卧空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[204] 查询: 打开主卧空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[205] 查询: 打开客厅东空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[206] 查询: 打开客厅西空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[207] 查询: 打开空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[208] 查询: 打开全家的空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[209] 查询: 关闭主卧的空调一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[210] 查询: 关闭主卧的空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[211] 查询: 关闭空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[212] 查询: 关闭全家的空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[213] 查询: 主卧的空调一的温度设成26度
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[214] 查询: 主卧的空调温度设成26度
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[215] 查询: 空调温度设成26度
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[216] 查询: 空调一的温度设成26度
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[217] 查询: 全家的空调温度设成26度
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[218] 查询: 主卧的主卧空调的温度调高一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[219] 查询: 主卧的空调的温度调高一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[220] 查询: 空调一的温度调高一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[221] 查询: 空调的温度调高一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[222] 查询: 全家的空调的温度调高一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[223] 查询: 主卧的主卧空调的温度调到最高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[224] 查询: 主卧的空调的温度调到最高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[225] 查询: 空调一的温度调到最高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[226] 查询: 空调的温度调到最高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[227] 查询: 全家的空调的温度调到最高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[228] 查询: 主卧的主卧空调的温度调低一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[229] 查询: 主卧的空调的温度调低一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[230] 查询: 空调一的温度调低一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[231] 查询: 空调的温度调低一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[232] 查询: 全家的空调的温度调低一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[233] 查询: 主卧的主卧空调的温度调到最低
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[234] 查询: 主卧的空调的温度调到最低
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[235] 查询: 空调一的温度调到最低
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[236] 查询: 空调的温度调到最低
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[237] 查询: 全家的空调的温度调到最低
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[238] 查询: 空调一的工作模式设成制冷模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[239] 查询: 空调一的工作模式设成制热模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[240] 查询: 空调一的工作模式设成送风模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[241] 查询: 空调一的工作模式设成换气模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[242] 查询: 空调一的工作模式设成除湿模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[243] 查询: 主卧的主卧空调的模式设成制冷模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[244] 查询: 主卧的空调设成制冷模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[245] 查询: 空调设成制冷模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[246] 查询: 空调一设成制冷模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[247] 查询: 全家的空调设成制冷模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[248] 查询: 空调一的风速设成高速
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[249] 查询: 空调一的风速设成中速
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[250] 查询: 空调一的风速设成低速
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[251] 查询: 空调一的风速设成自动
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[252] 查询: 主卧的主卧空调的风速设成自动
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[253] 查询: 主卧的空调的风速设成自动
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[254] 查询: 空调的风速设成自动
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[255] 查询: 空调一的风速设成自动
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[256] 查询: 全家的空调的风速设成自动
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[257] 查询: 打开次卧的开关一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[258] 查询: 打开次卧的开关
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[259] 查询: 打开客厅的过道三开
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[260] 查询: 打开客厅的餐厅筒灯
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[261] 查询: 打开客厅的筒灯
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[262] 查询: 打开客厅的灯
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[263] 查询: 打开开关
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[264] 查询: 打开开关一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[265] 查询: 打开全家的开关
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[266] 查询: 关闭主卧的主卧双开
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[267] 查询: 关闭主卧的开关
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[268] 查询: 关闭开关
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[269] 查询: 关闭开关一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[270] 查询: 关闭全家的开关
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[271] 查询: 打开书房的开关三
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[272] 查询: 打开"餐厅筒灯"
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[273] 查询: 关闭书房的"餐厅筒灯"
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[274] 查询: 关闭客厅的餐厅筒灯
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[275] 查询: 关闭客厅的筒灯
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[276] 查询: 关闭客厅的灯
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[277] 查询: 关闭开关三
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[278] 查询: 打开主卧的插座一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[279] 查询: 打开主卧的插座
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[280] 查询: 打开插座一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[281] 查询: 打开插座
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[282] 查询: 打开全家的插座
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[283] 查询: 关闭主卧的插座一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[284] 查询: 关闭主卧的插座
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[285] 查询: 关闭插座一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[286] 查询: 关闭插座
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[287] 查询: 关闭全家的插座
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[288] 查询: 打开主卧的地暖一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[289] 查询: 打开主卧的地暖
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[290] 查询: 打开地暖一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[291] 查询: 打开地暖
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[292] 查询: 打开全家的地暖
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[293] 查询: 关闭主卧的地暖一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[294] 查询: 关闭主卧的地暖
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[295] 查询: 关闭地暖一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[296] 查询: 关闭地暖
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[297] 查询: 关闭全家的地暖
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[298] 查询: 打开主卧的新风
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[299] 查询: 打开新风
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[300] 查询: 打开新风一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[301] 查询: 打开全家的新风机
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[302] 查询: 关闭主卧的新风
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[303] 查询: 关闭新风
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[304] 查询: 关闭新风一
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[305] 查询: 关闭全家的新风机
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[306] 查询: 主卧新风的风速设成自动
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[307] 查询: 主卧新风的风速设成高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[308] 查询: 主卧新风的风速设成中
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[309] 查询: 主卧新风的风速设成低
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[310] 查询: 新风机风速调高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[311] 查询: 新风机风速调低
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[312] 查询: 新风机打开杀菌
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[313] 查询: 新风机关闭杀菌
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[314] 查询: 打开安防
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[315] 查询: 关闭安防
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[316] 查询: 小零小零
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[317] 查询: 查询天气
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[318] 查询: 关闭全家的灯
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[319] 查询: 查询我的设备数量
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[320] 查询: 退下吧
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[321] 查询: 重新开始
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[322] 查询: 介绍几个景点给我
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[323] 查询: 你是谁
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[324] 查询: 油烟机风速调大一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[325] 查询: 电饭煲开始煮饭
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[326] 查询: 微波炉剩余烹饪时长
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[327] 查询: 洗衣机调到快速洗
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[328] 查询: 新风机设为睡眠模式
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[329] 查询: 关闭卧室所有灯
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[330] 查询: 按摩椅开始按摩
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[331] 查询: 打开加湿器
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[332] 查询: 打开投影
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[333] 查询: 灯亮度和色温调低一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[334] 查询: 灯亮度和色温调高一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[335] 查询: 灯亮度调到80%色温调到3700k
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[336] 查询: 打开主卧的灯然后亮度和色温调低一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[337] 查询: 打开主卧的灯然后亮度和色温调高一点
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[338] 查询: 打开主卧的灯然后亮度调到80%色温调到3700k
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[339] 查询: 打开全家的灯并把亮度调到80%色温调到3700k
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[340] 查询: 空调的温度调低一点并调成制冷
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[341] 查询: 空调的温度调到26度模式调成制热
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[342] 查询: 空调的温度调到26度模式调成制热风速调高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[343] 查询: 客厅空调的温度调到26度模式调成制热风速调高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[344] 查询: 打开客厅的空调然后温度调到26度模式调成制热风速调高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[345] 查询: 打开全家的空调然后温度调到26度模式调成制热风速调高
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[346] 查询: 打开灯和空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[347] 查询: 打开灯和地暖
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[348] 查询: 打开灯和新风
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[349] 查询: 打开空调和新风
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[350] 查询: 打开主卧的灯和空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[351] 查询: 打开全家的灯和空调
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[352] 查询: 打开灯和空调还有窗帘
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[353] 查询: 打开主卧的灯和空调还有窗帘
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[354] 查询: 打开全家的灯和空调还有窗帘
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[355] 查询: 打开主卧的所有设备
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[356] 查询: 打开氛围灯然后亮度调低色温调低
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[357] 查询: 打开氛围灯然后亮度调到50色温调到4000
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

[358] 查询: 打开空调一工作模式调成制热温度调到26度风速调成高速
状态: ❌ 失败
请求耗时: 0.00秒
响应: {
  "code": "ERROR",
  "msg": "连接失败，请检查服务是否启动",
  "data": null
}
------------------------------------------------------------

