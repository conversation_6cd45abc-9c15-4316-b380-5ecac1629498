智能家居语音指令测试结果
生成时间: 2025-08-04 18:28:24
总测试数量: 358
================================================================================

[1] 查询: 北京的天气怎么样
状态: ✅ 成功
请求耗时: 1.75秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：北京；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[2] 查询: 深圳的天气怎么样
状态: ✅ 成功
请求耗时: 2.04秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：深圳；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[3] 查询: 顺德的天气怎么样
状态: ✅ 成功
请求耗时: 1.64秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：广东省佛山市顺德区北滘镇；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[4] 查询: 佛山的天气怎么样
状态: ✅ 成功
请求耗时: 1.56秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：广东省佛山市顺德区北滘镇；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[5] 查询: 广东的天气怎么样
状态: ✅ 成功
请求耗时: 1.83秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：广东省；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[6] 查询: 广州市南沙区的天气怎么样
状态: ✅ 成功
请求耗时: 2.97秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：广州市南沙区；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[7] 查询: 今天天气怎么样
状态: ✅ 成功
请求耗时: 1.72秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[8] 查询: 明天天气怎么样
状态: ✅ 成功
请求耗时: 1.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；day：+1"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[9] 查询: 后天天气怎么样
状态: ✅ 成功
请求耗时: 1.89秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；day：+2"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[10] 查询: 未来一周天气怎么样
状态: ✅ 成功
请求耗时: 1.77秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；timeRange：【（day：+0）～（day：+6）】"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[11] 查询: 未来两周天气怎么样
状态: ✅ 成功
请求耗时: 1.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；day：+2"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[12] 查询: 过去一周天气怎么样
状态: ✅ 成功
请求耗时: 1.54秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；day：-1"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[13] 查询: 过去两周天气怎么样
状态: ✅ 成功
请求耗时: 1.78秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；day：-1"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[14] 查询: 深圳今天的天气怎么样
状态: ✅ 成功
请求耗时: 1.65秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：深圳；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[15] 查询: 深圳明天的天气怎么样
状态: ✅ 成功
请求耗时: 11.98秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：深圳；day：+1"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[16] 查询: 深圳后天的天气怎么样
状态: ✅ 成功
请求耗时: 1.61秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：深圳；day：+2"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[17] 查询: 广东今天的天气怎么样
状态: ✅ 成功
请求耗时: 1.57秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：广东省；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[18] 查询: 深圳市南山区今天的天气怎么样
状态: ✅ 成功
请求耗时: 2.19秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；pos：深圳市南山区；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[19] 查询: 今天的温度
状态: ✅ 成功
请求耗时: 1.86秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询温度/体感温度；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[20] 查询: 今天的日出时间
状态: ✅ 成功
请求耗时: 6.52秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询日出/日落时间；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[21] 查询: 今天的日落时间
状态: ✅ 成功
请求耗时: 2.16秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询日出/日落时间；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[22] 查询: 今天的空气湿度
状态: ✅ 成功
请求耗时: 1.97秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询空气湿度；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[23] 查询: 今天的空气质量
状态: ✅ 成功
请求耗时: 7.10秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询空气质量&空气污染扩散指数；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[24] 查询: 今天的风速
状态: ✅ 成功
请求耗时: 2.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询风速/风向；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[25] 查询: 今天的限行信息
状态: ✅ 成功
请求耗时: 7.22秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询限行；pos：广东省佛山市顺德区北滘镇；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[26] 查询: 广州的限行信息
状态: ✅ 成功
请求耗时: 1.55秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询限行；pos：广州；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[27] 查询: 今天广州的限行信息
状态: ✅ 成功
请求耗时: 1.78秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询限行；pos：广州；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[28] 查询: 化妆指数
状态: ✅ 成功
请求耗时: 1.72秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询化妆指数；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[29] 查询: 紫外线指数
状态: ✅ 成功
请求耗时: 1.63秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询紫外线指数；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[30] 查询: 感冒指数
状态: ✅ 成功
请求耗时: 1.73秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询感冒指数；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[31] 查询: 洗车指数
状态: ✅ 成功
请求耗时: 1.58秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询洗车指数；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[32] 查询: 穿衣指数
状态: ✅ 成功
请求耗时: 2.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询穿衣指数；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[33] 查询: 运动指数
状态: ✅ 成功
请求耗时: 14.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询运动指数；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[34] 查询: 钓鱼指数
状态: ✅ 成功
请求耗时: 1.75秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询钓鱼指数；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[35] 查询: 交通指数
状态: ✅ 成功
请求耗时: 1.94秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询限行；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[36] 查询: 过敏指数
状态: ✅ 成功
请求耗时: 2.19秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：生活指数；intent：查询感冒指数；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[37] 查询: 打开主卧的筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[38] 查询: 打开主卧的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[39] 查询: 打开明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[40] 查询: 打开筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[41] 查询: 打开全屋的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:全屋;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[42] 查询: 关闭主卧的筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:主卧;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[43] 查询: 关闭主卧的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:主卧;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[44] 查询: 关闭明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[45] 查询: 关闭过道三开
状态: ❌ 失败
请求耗时: 1.57秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[46] 查询: 关闭全屋的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:全屋;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[47] 查询: 主卧的吸顶灯一亮度调高一点
状态: ✅ 成功
请求耗时: 2.13秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：吸顶灯一；domain：灯；intent：调高亮度；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[48] 查询: 主卧的明装筒灯亮度调高一点
状态: ✅ 成功
请求耗时: 2.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：调高亮度；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[49] 查询: 主卧的灯亮度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高亮度;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[50] 查询: 主卧的吸顶灯一亮度调到最高
状态: ✅ 成功
请求耗时: 2.59秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：吸顶灯一；domain：灯；intent：设置亮度；room：主卧；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[51] 查询: 主卧的明装筒灯亮度调到最高
状态: ✅ 成功
请求耗时: 2.09秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：设置亮度；room：主卧；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[52] 查询: 主卧的灯亮度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高亮度;domain:灯;room:主卧;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[53] 查询: 调高吸顶灯一的亮度
状态: ✅ 成功
请求耗时: 1.86秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：吸顶灯一；domain：灯；intent：调高亮度"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[54] 查询: 调高明装筒灯的亮度
状态: ✅ 成功
请求耗时: 7.57秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：调高亮度"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[55] 查询: 调高全屋的明装筒灯的亮度
状态: ✅ 成功
请求耗时: 2.47秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调高亮度；room：全屋；device：明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[56] 查询: 调高全屋灯的亮度
状态: ✅ 成功
请求耗时: 7.22秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调高亮度；room：全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[57] 查询: 吸顶灯一的亮度调到最高
状态: ✅ 成功
请求耗时: 2.02秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：吸顶灯一；domain：灯；intent：设置亮度；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[58] 查询: 明装筒灯的亮度调到最高
状态: ✅ 成功
请求耗时: 1.84秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：设置亮度；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[59] 查询: 全屋的明装筒灯的亮度调到最高
状态: ✅ 成功
请求耗时: 2.02秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋明装筒灯；domain：灯；intent：设置亮度；room：全屋；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[60] 查询: 全屋灯的亮度调到最高
状态: ✅ 成功
请求耗时: 1.53秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调高亮度；room：全屋；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[61] 查询: 主卧的吸顶灯一亮度调低一点
状态: ✅ 成功
请求耗时: 1.50秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：吸顶灯一；domain：灯；intent：调低亮度；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[62] 查询: 主卧的明装筒灯亮度调低一点
状态: ✅ 成功
请求耗时: 1.76秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：调低亮度；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[63] 查询: 主卧的灯亮度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低亮度;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[64] 查询: 主卧的吸顶灯一亮度调到最低
状态: ✅ 成功
请求耗时: 2.02秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：吸顶灯一；domain：灯；intent：设置亮度；room：主卧；value：min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[65] 查询: 主卧的明装筒灯亮度调到最低
状态: ✅ 成功
请求耗时: 1.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：设置亮度；room：主卧；value：min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[66] 查询: 主卧的灯亮度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低亮度;domain:灯;room:主卧;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[67] 查询: 调低吸顶灯一的亮度
状态: ✅ 成功
请求耗时: 1.78秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：吸顶灯一；domain：灯；intent：调低亮度"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[68] 查询: 调低明装筒灯的亮度
状态: ✅ 成功
请求耗时: 1.90秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：调低亮度"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[69] 查询: 调低全屋明装筒灯的亮度
状态: ✅ 成功
请求耗时: 1.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋明装筒灯；domain：灯；intent：调低亮度；room：全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[70] 查询: 调低全屋灯的亮度
状态: ✅ 成功
请求耗时: 1.56秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋灯；domain：灯；intent：调低亮度；room：全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[71] 查询: 吸顶灯一的亮度调到最低
状态: ✅ 成功
请求耗时: 1.48秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：吸顶灯一；domain：灯；intent：设置亮度；value：min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[72] 查询: 明装筒灯的亮度调到最低
状态: ✅ 成功
请求耗时: 2.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调低亮度；device：明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[73] 查询: 全屋明装筒灯的亮度调到最低
状态: ✅ 成功
请求耗时: 6.95秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋明装筒灯；domain：灯；intent：调低亮度；room：全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[74] 查询: 全屋灯的亮度调到最低
状态: ✅ 成功
请求耗时: 1.73秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋灯；domain：灯；intent：设置亮度；room：全屋；value：min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[75] 查询: 主卧的明装筒灯一亮度设成80%
状态: ✅ 成功
请求耗时: 1.89秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置亮度；room：主卧；device：明装筒灯一；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[76] 查询: 主卧的明装筒灯亮度设成80%
状态: ✅ 成功
请求耗时: 1.70秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：设置亮度；room：主卧；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[77] 查询: 主卧的灯亮度亮度设成80%
状态: ✅ 成功
请求耗时: 2.04秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置亮度；room：主卧；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[78] 查询: 明装筒灯一亮度设成80%
状态: ✅ 成功
请求耗时: 8.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：设置亮度；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[79] 查询: 明装筒灯亮度为80%
状态: ✅ 成功
请求耗时: 1.66秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：设置亮度；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[80] 查询: 全屋的明装筒灯亮度设成80%
状态: ✅ 成功
请求耗时: 1.96秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋明装筒灯；domain：灯；intent：设置亮度；room：全屋；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[81] 查询: 全屋灯的亮度设成80%
状态: ✅ 成功
请求耗时: 1.60秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置亮度；room：全屋；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[82] 查询: 主卧的明装筒灯一色温调高一点
状态: ✅ 成功
请求耗时: 1.97秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：调高色温；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[83] 查询: 主卧的明装筒灯色温调高一点
状态: ✅ 成功
请求耗时: 1.83秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：调高色温；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[84] 查询: 主卧的明装筒灯一色温调到最高
状态: ✅ 成功
请求耗时: 1.50秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：设置色温；room：主卧；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[85] 查询: 主卧的明装筒灯色温调到最高
状态: ✅ 成功
请求耗时: 1.65秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：设置色温；room：主卧；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[86] 查询: 主卧色温调高一点
状态: ✅ 成功
请求耗时: 2.06秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调高色温；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[87] 查询: 明装筒灯一的色温调高一点
状态: ✅ 成功
请求耗时: 1.59秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：调高色温"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[88] 查询: 主卧色温调到最高
状态: ✅ 成功
请求耗时: 1.73秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置色温；room：主卧；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[89] 查询: 明装筒灯一的色温调到最高
状态: ✅ 成功
请求耗时: 1.61秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：调高色温；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[90] 查询: 明装筒灯的色温调高一点
状态: ✅ 成功
请求耗时: 1.96秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调高色温；device：明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[91] 查询: 调高全屋明装筒灯的色温
状态: ✅ 成功
请求耗时: 1.61秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋明装筒灯；domain：灯；intent：调高色温；room：全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[92] 查询: 明装筒灯的色温调到最高
状态: ✅ 成功
请求耗时: 7.10秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：调高色温；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[93] 查询: 全屋明装筒灯的色温调到最高
状态: ✅ 成功
请求耗时: 1.88秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋明装筒灯；domain：灯；intent：调高色温；room：全屋；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[94] 查询: 调高全屋灯的色温
状态: ✅ 成功
请求耗时: 2.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调高色温；room：全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[95] 查询: 主卧的明装筒灯一色温调低一点
状态: ✅ 成功
请求耗时: 2.53秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：调低色温；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[96] 查询: 主卧的明装筒灯色温调低一点
状态: ✅ 成功
请求耗时: 1.88秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调低色温；room：主卧；device：明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[97] 查询: 主卧的明装筒灯一色温调到最低
状态: ✅ 成功
请求耗时: 2.10秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：设置色温；room：主卧；value：min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[98] 查询: 主卧的明装筒灯色温调到最低
状态: ✅ 成功
请求耗时: 1.78秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：设置色温；room：主卧；value：min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[99] 查询: 主卧色温调低一点
状态: ✅ 成功
请求耗时: 1.61秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调低色温；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[100] 查询: 明装筒灯一的色温调低一点
状态: ✅ 成功
请求耗时: 1.80秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：调低色温"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[101] 查询: 主卧色温调到最低
状态: ✅ 成功
请求耗时: 1.84秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置色温；room：主卧；value：min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[102] 查询: 明装筒灯一的色温调到最低
状态: ✅ 成功
请求耗时: 1.67秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：调低色温"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[103] 查询: 明装筒灯的色温调低一点
状态: ✅ 成功
请求耗时: 2.16秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：调低色温"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[104] 查询: 调低全屋明装筒灯的色温
状态: ✅ 成功
请求耗时: 2.32秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调低色温；room：全屋；device：全屋明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[105] 查询: 调低全屋灯的色温
状态: ✅ 成功
请求耗时: 1.79秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋灯；domain：灯；intent：调低色温；room：全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[106] 查询: 全屋明装筒灯的色温调到最低
状态: ✅ 成功
请求耗时: 1.87秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：全屋明装筒灯；domain：灯；intent：调低色温；room：全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[107] 查询: 全屋灯的色温调到最低
状态: ✅ 成功
请求耗时: 3.19秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调低色温；room：全屋；value：min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[108] 查询: 主卧的明装筒灯一色温设成3700
状态: ✅ 成功
请求耗时: 7.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：设置色温；room：主卧；value：3700"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[109] 查询: 主卧的明装筒灯色温设成3700
状态: ✅ 成功
请求耗时: 2.07秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置色温；room：主卧；device：明装筒灯；value：3700"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[110] 查询: 主卧色温设成3700
状态: ✅ 成功
请求耗时: 1.94秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置色温；room：主卧；value：3700"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[111] 查询: 明装筒灯一的色温设成3700
状态: ✅ 成功
请求耗时: 1.67秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯一；domain：灯；intent：设置色温；value：3700"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[112] 查询: 明装筒灯的色温设成3700
状态: ✅ 成功
请求耗时: 1.80秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：明装筒灯；domain：灯；intent：设置色温；value：3700"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[113] 查询: 全屋明装筒灯的色温设成3700
状态: ✅ 成功
请求耗时: 2.07秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置色温；room：全屋；device：全屋明装筒灯；value：3700"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[114] 查询: 全屋灯的色温设成3700
状态: ✅ 成功
请求耗时: 1.68秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置色温；room：全屋；value：3700"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[115] 查询: 打开主卧的一路通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:主卧;device:一路通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[116] 查询: 打开主卧的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[117] 查询: 打开通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[118] 查询: 打开通断器一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;device:通断器一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[119] 查询: 打开全屋的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[120] 查询: 打开6路通断器灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:6路通断器灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[121] 查询: 打开海上的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:海上;device:海上的灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[122] 查询: 关闭主卧的一路通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:主卧;device:一路通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[123] 查询: 关闭主卧的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[124] 查询: 关闭通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[125] 查询: 关闭通断器一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;device:通断器一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[126] 查询: 关闭全屋的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[127] 查询: 关闭6路通断器灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;device:6路通断器灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[128] 查询: 打开主卧的纱帘
状态: ❌ 失败
请求耗时: 2.03秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[129] 查询: 打开主卧的布帘
状态: ❌ 失败
请求耗时: 1.62秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[130] 查询: 打开主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[131] 查询: 打开窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[132] 查询: 打开纱帘
状态: ❌ 失败
请求耗时: 1.33秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[133] 查询: 打开布帘
状态: ❌ 失败
请求耗时: 1.59秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[134] 查询: 打开窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[135] 查询: 打开全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[136] 查询: 关闭主卧的纱帘
状态: ❌ 失败
请求耗时: 1.45秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[137] 查询: 关闭主卧的布帘
状态: ❌ 失败
请求耗时: 1.58秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[138] 查询: 关闭主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[139] 查询: 关闭窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[140] 查询: 关闭纱帘
状态: ❌ 失败
请求耗时: 1.24秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[141] 查询: 关闭布帘
状态: ❌ 失败
请求耗时: 1.58秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[142] 查询: 关上窗帘
状态: ✅ 成功
请求耗时: 1.63秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent：关闭窗帘；domain：窗帘"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[143] 查询: 关闭全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[144] 查询: 暂停主卧的纱帘
状态: ❌ 失败
请求耗时: 1.59秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[145] 查询: 暂停主卧的布帘
状态: ❌ 失败
请求耗时: 1.70秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[146] 查询: 暂停主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[147] 查询: 暂停窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[148] 查询: 暂停纱帘
状态: ❌ 失败
请求耗时: 1.70秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[149] 查询: 暂停布帘
状态: ❌ 失败
请求耗时: 1.98秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[150] 查询: 暂停窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[151] 查询: 暂停全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[152] 查询: 主卧的纱帘开合度设成80%
状态: ✅ 成功
请求耗时: 2.07秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：纱帘；domain：窗帘；intent：设置开合度；room：主卧；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[153] 查询: 主卧的布帘开合度设成80%
状态: ✅ 成功
请求耗时: 1.61秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：布帘；domain：窗帘；intent：设置开合度；room：主卧；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[154] 查询: 主卧的窗帘电机开合度设成80%
状态: ✅ 成功
请求耗时: 1.56秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：窗帘电机；domain：窗帘；intent：设置开合度；room：主卧；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[155] 查询: 主卧的开合度设成80%
状态: ✅ 成功
请求耗时: 1.78秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：的开合度；domain：窗帘；intent：设置开合度；room：主卧；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[156] 查询: 窗帘电机开合度设成80%
状态: ✅ 成功
请求耗时: 11.91秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：窗帘电机；domain：窗帘；intent：设置开合度；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[157] 查询: 纱帘开合度设成80%
状态: ✅ 成功
请求耗时: 1.83秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：窗帘；intent：设置开合度；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[158] 查询: 布帘开合度设成80%
状态: ✅ 成功
请求耗时: 2.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：布帘；domain：窗帘；intent：设置开合度；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[159] 查询: 全屋的开合度设成80%
状态: ✅ 成功
请求耗时: 1.67秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：窗帘；intent：设置开合度；room：全屋；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[160] 查询: 打开主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[161] 查询: 打开主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[162] 查询: 打开窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[163] 查询: 打开窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[164] 查询: 打开全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[165] 查询: 打开窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[166] 查询: 关闭主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[167] 查询: 关闭主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[168] 查询: 关闭窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[169] 查询: 关闭窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[170] 查询: 关闭全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[171] 查询: 关闭窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[172] 查询: 暂停主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[173] 查询: 暂停主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[174] 查询: 暂停窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[175] 查询: 暂停窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[176] 查询: 暂停全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[177] 查询: 暂停窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[178] 查询: 回家模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[179] 查询: 到家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[180] 查询: 回来了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[181] 查询: 回家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[182] 查询: 离家模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[183] 查询: 我走了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[184] 查询: 出门
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[185] 查询: 上班去了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[186] 查询: 外出
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[187] 查询: 我离家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[188] 查询: 旅游
状态: ✅ 成功
请求耗时: 2.78秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "content：旅游是一种放松身心、探索世界的好方式。推荐几个热门旅游目的地：1. 九寨沟，以其五彩斑斓的湖泊和瀑布闻名，是自然风光的绝佳代表；2. 张家界国家森林公园，拥有奇特的石英砂岩峰林地貌，电影《阿凡达》的取景地之一；3. 厦门鼓浪屿，一座充满文艺气息的小岛，有丰富的历史建筑和美丽的海滩。这些地方都能让你体验到不同的文化和美景。；domain：闲聊；intent：闲聊"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[189] 查询: 用餐模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:用餐模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[190] 查询: 我要吃饭了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:用餐模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[191] 查询: 会客模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:会客模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[192] 查询: 有客人来了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:会客模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[193] 查询: 来客人了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:会客模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[194] 查询: 晨起模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:晨起模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[195] 查询: 我要起床了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:晨起模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[196] 查询: 我醒了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:晨起模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[197] 查询: 睡眠模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:睡眠模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[198] 查询: 我要睡觉了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:睡眠模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[199] 查询: 执行全开模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:全开模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[200] 查询: 执行全关模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:全关模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[201] 查询: 打开主卧的空调一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧;device:空调一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[202] 查询: 打开主卧的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[203] 查询: 打开主卧的主卧空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧;device:主卧空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[204] 查询: 打开主卧空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧;device:主卧空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[205] 查询: 打开客厅东空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:客厅;device:客厅东空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[206] 查询: 打开客厅西空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:客厅;device:客厅西空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[207] 查询: 打开空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[208] 查询: 打开全家的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[209] 查询: 关闭主卧的空调一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调;room:主卧;device:空调一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[210] 查询: 关闭主卧的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[211] 查询: 关闭空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[212] 查询: 关闭全家的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[213] 查询: 主卧的空调一的温度设成26度
状态: ✅ 成功
请求耗时: 2.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置温度；room：主卧；value：26"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[214] 查询: 主卧的空调温度设成26度
状态: ✅ 成功
请求耗时: 1.67秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：主卧空调；domain：空调；intent：设置温度；room：主卧；value：26"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[215] 查询: 空调温度设成26度
状态: ✅ 成功
请求耗时: 1.32秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：设置温度；value：26"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[216] 查询: 空调一的温度设成26度
状态: ✅ 成功
请求耗时: 1.82秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置温度；value：26"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[217] 查询: 全家的空调温度设成26度
状态: ✅ 成功
请求耗时: 1.66秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：设置温度；room：all；value：26"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[218] 查询: 主卧的主卧空调的温度调高一点
状态: ✅ 成功
请求耗时: 1.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：主卧空调；domain：空调；intent：调高温度；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[219] 查询: 主卧的空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[220] 查询: 空调一的温度调高一点
状态: ✅ 成功
请求耗时: 1.51秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：调高温度"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[221] 查询: 空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[222] 查询: 全家的空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[223] 查询: 主卧的主卧空调的温度调到最高
状态: ✅ 成功
请求耗时: 1.52秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：主卧空调；domain：空调；intent：设置温度；room：主卧；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[224] 查询: 主卧的空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:主卧;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[225] 查询: 空调一的温度调到最高
状态: ✅ 成功
请求耗时: 1.77秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置温度；value：max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[226] 查询: 空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[227] 查询: 全家的空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:all;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[228] 查询: 主卧的主卧空调的温度调低一点
状态: ✅ 成功
请求耗时: 1.63秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：主卧空调；domain：空调；intent：调低温度；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[229] 查询: 主卧的空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[230] 查询: 空调一的温度调低一点
状态: ✅ 成功
请求耗时: 1.68秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：调低温度"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[231] 查询: 空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[232] 查询: 全家的空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[233] 查询: 主卧的主卧空调的温度调到最低
状态: ✅ 成功
请求耗时: 1.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：主卧空调；domain：空调；intent：设置温度；room：主卧；value：最低"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[234] 查询: 主卧的空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:主卧;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[235] 查询: 空调一的温度调到最低
状态: ✅ 成功
请求耗时: 1.53秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置温度；value：最低"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[236] 查询: 空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[237] 查询: 全家的空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:all;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[238] 查询: 空调一的工作模式设成制冷模式
状态: ✅ 成功
请求耗时: 1.67秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置模式；value：制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[239] 查询: 空调一的工作模式设成制热模式
状态: ✅ 成功
请求耗时: 1.58秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：设置模式；device：空调一；value：制热"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[240] 查询: 空调一的工作模式设成送风模式
状态: ✅ 成功
请求耗时: 1.47秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置模式；value：送风"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[241] 查询: 空调一的工作模式设成换气模式
状态: ✅ 成功
请求耗时: 1.64秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置模式；value：换气"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[242] 查询: 空调一的工作模式设成除湿模式
状态: ✅ 成功
请求耗时: 1.57秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置模式；value：除湿"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[243] 查询: 主卧的主卧空调的模式设成制冷模式
状态: ✅ 成功
请求耗时: 1.28秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：设置模式；room：主卧；value：制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[244] 查询: 主卧的空调设成制冷模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置模式;domain:空调;room:主卧;value:制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[245] 查询: 空调设成制冷模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置模式;domain:空调;value:制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[246] 查询: 空调一设成制冷模式
状态: ✅ 成功
请求耗时: 1.59秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：设置模式；value：制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[247] 查询: 全家的空调设成制冷模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置模式;domain:空调;room:all;value:制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[248] 查询: 空调一的风速设成高速
状态: ✅ 成功
请求耗时: 2.06秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置风速；value：高"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[249] 查询: 空调一的风速设成中速
状态: ✅ 成功
请求耗时: 1.68秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置风速；value：中"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[250] 查询: 空调一的风速设成低速
状态: ✅ 成功
请求耗时: 6.96秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置风速；value：低"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[251] 查询: 空调一的风速设成自动
状态: ✅ 成功
请求耗时: 2.21秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：设置模式；device：空调一；value：自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[252] 查询: 主卧的主卧空调的风速设成自动
状态: ✅ 成功
请求耗时: 1.73秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：设置模式；room：主卧；value：自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[253] 查询: 主卧的空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:空调;room:主卧;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[254] 查询: 空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:空调;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[255] 查询: 空调一的风速设成自动
状态: ✅ 成功
请求耗时: 6.49秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "device：空调一；domain：空调；intent：设置风速；value：自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[256] 查询: 全家的空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:空调;room:all;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[257] 查询: 打开次卧的开关一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;room:次卧;device:开关一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[258] 查询: 打开次卧的开关
状态: ✅ 成功
请求耗时: 6.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：开关；intent：打开开关；room：次卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[259] 查询: 打开客厅的过道三开
状态: ❌ 失败
请求耗时: 1.79秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[260] 查询: 打开客厅的餐厅筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:客厅;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[261] 查询: 打开客厅的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:客厅;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[262] 查询: 打开客厅的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:客厅"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[263] 查询: 打开开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[264] 查询: 打开开关一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;device:开关一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[265] 查询: 打开全家的开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[266] 查询: 关闭主卧的主卧双开
状态: ❌ 失败
请求耗时: 1.41秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[267] 查询: 关闭主卧的开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[268] 查询: 关闭开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[269] 查询: 关闭开关一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;device:开关一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[270] 查询: 关闭全家的开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[271] 查询: 打开书房的开关三
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;room:书房;device:开关三"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[272] 查询: 打开"餐厅筒灯"
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:餐厅;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[273] 查询: 关闭书房的"餐厅筒灯"
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:书房;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[274] 查询: 关闭客厅的餐厅筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:客厅;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[275] 查询: 关闭客厅的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:客厅;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[276] 查询: 关闭客厅的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:客厅"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[277] 查询: 关闭开关三
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;device:开关三"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[278] 查询: 打开主卧的插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;room:主卧;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[279] 查询: 打开主卧的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[280] 查询: 打开插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[281] 查询: 打开插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[282] 查询: 打开全家的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[283] 查询: 关闭主卧的插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;room:主卧;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[284] 查询: 关闭主卧的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[285] 查询: 关闭插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[286] 查询: 关闭插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[287] 查询: 关闭全家的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[288] 查询: 打开主卧的地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;room:主卧;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[289] 查询: 打开主卧的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[290] 查询: 打开地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[291] 查询: 打开地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[292] 查询: 打开全家的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[293] 查询: 关闭主卧的地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;room:主卧;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[294] 查询: 关闭主卧的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[295] 查询: 关闭地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[296] 查询: 关闭地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[297] 查询: 关闭全家的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[298] 查询: 打开主卧的新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[299] 查询: 打开新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[300] 查询: 打开新风一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;device:新风一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[301] 查询: 打开全家的新风机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;room:all;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[302] 查询: 关闭主卧的新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[303] 查询: 关闭新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[304] 查询: 关闭新风一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;device:新风一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[305] 查询: 关闭全家的新风机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;room:all;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[306] 查询: 主卧新风的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[307] 查询: 主卧新风的风速设成高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:高"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[308] 查询: 主卧新风的风速设成中
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:中"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[309] 查询: 主卧新风的风速设成低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:低"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[310] 查询: 新风机风速调高
状态: ✅ 成功
请求耗时: 1.51秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：新风；intent：调高风速"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[311] 查询: 新风机风速调低
状态: ✅ 成功
请求耗时: 1.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：新风；intent：调低风速"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[312] 查询: 新风机打开杀菌
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[313] 查询: 新风机关闭杀菌
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[314] 查询: 打开安防
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开安防;domain:安防"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[315] 查询: 关闭安防
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭安防;domain:安防"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[316] 查询: 小零小零
状态: ✅ 成功
请求耗时: 1.58秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent：重新开始"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[317] 查询: 查询天气
状态: ✅ 成功
请求耗时: 1.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：天气；intent：查询天气状况；day：+0"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[318] 查询: 关闭全家的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[319] 查询: 查询我的设备数量
状态: ✅ 成功
请求耗时: 1.63秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent：设备数量查询；domain：all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[320] 查询: 退下吧
状态: ✅ 成功
请求耗时: 1.29秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：对话；intent：终止对话"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[321] 查询: 重新开始
状态: ✅ 成功
请求耗时: 1.21秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent：重新开始"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[322] 查询: 介绍几个景点给我
状态: ✅ 成功
请求耗时: 3.31秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "content：广东省佛山市顺德区北滘镇附近有以下几个景点推荐：\n\n1. 顺峰山公园：这是一个集自然风光与人文景观于一体的大型城市公园，园内有湖泊、山丘和古建筑，适合散步、骑行和野餐。\n\n2. 南国桃园：一个以桃花为主题的园林景区，四季花开不断，尤其在春季桃花盛开时非常壮观，是拍照打卡的好地方。\n\n3. 陈村花卉世界：这里是全国最大的花卉交易市场之一，不仅有各种鲜花和绿植，还有专业的园艺展览和亲子活动，非常适合家庭出游。；domain：闲聊；intent：闲聊"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[323] 查询: 你是谁
状态: ✅ 成功
请求耗时: 1.25秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent：自我介绍"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[324] 查询: 油烟机风速调大一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[325] 查询: 电饭煲开始煮饭
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[326] 查询: 微波炉剩余烹饪时长
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[327] 查询: 洗衣机调到快速洗
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[328] 查询: 新风机设为睡眠模式
状态: ✅ 成功
请求耗时: 1.94秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：新风；intent：设置模式；value：睡眠"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[329] 查询: 关闭卧室所有灯
状态: ✅ 成功
请求耗时: 1.29秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent：关闭灯；domain：灯；room：卧室"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[330] 查询: 按摩椅开始按摩
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[331] 查询: 打开加湿器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[332] 查询: 打开投影
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[333] 查询: 灯亮度和色温调低一点
状态: ✅ 成功
请求耗时: 2.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调低亮度",
      "domain：灯；intent：调低色温"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[334] 查询: 灯亮度和色温调高一点
状态: ✅ 成功
请求耗时: 1.78秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调高色温",
      "domain：灯；intent：调高亮度"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[335] 查询: 灯亮度调到80%色温调到3700k
状态: ✅ 成功
请求耗时: 2.19秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置色温；value：3700",
      "domain：灯；intent：设置亮度；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[336] 查询: 打开主卧的灯然后亮度和色温调低一点
状态: ✅ 成功
请求耗时: 3.00秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调低亮度；room：主卧",
      "domain：灯；intent：打开灯；room：主卧",
      "domain：灯；intent：调低色温；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[337] 查询: 打开主卧的灯然后亮度和色温调高一点
状态: ✅ 成功
请求耗时: 3.18秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：调高色温；room：主卧",
      "domain：灯；intent：打开灯；room：主卧",
      "domain：灯；intent：调高亮度；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[338] 查询: 打开主卧的灯然后亮度调到80%色温调到3700k
状态: ✅ 成功
请求耗时: 3.35秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：灯；intent：设置亮度；room：主卧；value：80%",
      "domain：灯；intent：打开灯；room：主卧",
      "domain：灯；intent：设置色温；room：主卧；value：3700"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[339] 查询: 打开全家的灯并把亮度调到80%色温调到3700k
状态: ✅ 成功
请求耗时: 3.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent：打开灯；domain：灯；room：all",
      "intent：设置色温；domain：灯；room：all；value：3700",
      "intent：设置亮度；domain：灯；room：all；value：80%"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[340] 查询: 空调的温度调低一点并调成制冷
状态: ✅ 成功
请求耗时: 1.80秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：调低温度",
      "domain：空调；intent：设置模式；value：制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[341] 查询: 空调的温度调到26度模式调成制热
状态: ✅ 成功
请求耗时: 7.80秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：设置模式；value：制热",
      "domain：空调；intent：设置温度；value：26"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[342] 查询: 空调的温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 2.68秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：设置模式；value：制热",
      "domain：空调；intent：设置温度；value：26",
      "domain：空调；intent：调高风速"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[343] 查询: 客厅空调的温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 2.88秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "domain：空调；intent：调高风速；room：客厅",
      "domain：空调；intent：设置温度；room：客厅；value：26",
      "domain：空调；intent：设置模式；room：客厅；value：制热"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[344] 查询: 打开客厅的空调然后温度调到26度模式调成制热风速调高
状态: ❌ 失败
请求耗时: 3.39秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[345] 查询: 打开全家的空调然后温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 3.22秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent：设置模式；domain：空调；room：all；value：制热",
      "intent：调高风速；domain：空调；room：all",
      "intent：设置温度；domain：空调；room：all；value：26",
      "intent：打开空调；domain：空调；room：all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[346] 查询: 打开灯和空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调",
      "intent:打开灯;domain:灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[347] 查询: 打开灯和地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖",
      "intent:打开灯;domain:灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[348] 查询: 打开灯和新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风",
      "intent:打开灯;domain:灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[349] 查询: 打开空调和新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风",
      "intent:打开空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[350] 查询: 打开主卧的灯和空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧",
      "intent:打开空调;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[351] 查询: 打开全家的灯和空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:all",
      "intent:打开灯;domain:灯;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[352] 查询: 打开灯和空调还有窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调",
      "intent:打开窗帘;domain:窗帘",
      "intent:打开灯;domain:灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[353] 查询: 打开主卧的灯和空调还有窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧",
      "intent:打开空调;domain:空调;room:主卧",
      "intent:打开窗帘;domain:窗帘;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[354] 查询: 打开全家的灯和空调还有窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:all",
      "intent:打开窗帘;domain:窗帘;room:all",
      "intent:打开灯;domain:灯;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[355] 查询: 打开主卧的所有设备
状态: ✅ 成功
请求耗时: 5.21秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent：打开插座；domain：插座；room：主卧",
      "intent：打开安防；domain：安防；room：主卧",
      "intent：打开开关；domain：开关；room：主卧",
      "intent：打开窗帘；domain：窗帘；room：主卧",
      "intent：打开空调；domain：空调；room：主卧",
      "intent：打开地暖；domain：地暖；room：主卧",
      "intent：打开灯；domain：灯；room：主卧",
      "intent：打开通断器；domain：通断器；room：主卧",
      "intent：打开新风；domain：新风；room：主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[356] 查询: 打开氛围灯然后亮度调低色温调低
状态: ❌ 失败
请求耗时: 2.83秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[357] 查询: 打开氛围灯然后亮度调到50色温调到4000
状态: ❌ 失败
请求耗时: 3.10秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

[358] 查询: 打开空调一工作模式调成制热温度调到26度风速调成高速
状态: ❌ 失败
请求耗时: 3.91秒
响应: {
  "code": "ERROR",
  "msg": "识别失败: 'intentDomainName'",
  "data": null
}
------------------------------------------------------------

