智能家居语音指令测试结果
生成时间: 2025-08-04 18:13:38
总测试数量: 358
================================================================================

[1] 查询: 北京的天气怎么样
状态: ✅ 成功
请求耗时: 22.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.9138391017913818
  }
}
------------------------------------------------------------

[2] 查询: 深圳的天气怎么样
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.43885374069213867
  }
}
------------------------------------------------------------

[3] 查询: 顺德的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"顺德\"}}"
    ],
    "inference_time": 0.3693418502807617
  }
}
------------------------------------------------------------

[4] 查询: 佛山的天气怎么样
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"room\": \"佛山\"}}"
    ],
    "inference_time": 0.4102442264556885
  }
}
------------------------------------------------------------

[5] 查询: 广东的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"广东\"}}"
    ],
    "inference_time": 0.3689699172973633
  }
}
------------------------------------------------------------

[6] 查询: 广州市南沙区的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"广州市\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.3683791160583496
  }
}
------------------------------------------------------------

[7] 查询: 今天天气怎么样
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.3277420997619629
  }
}
------------------------------------------------------------

[8] 查询: 明天天气怎么样
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.3272883892059326
  }
}
------------------------------------------------------------

[9] 查询: 后天天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+2\"}}"
    ],
    "inference_time": 0.36670756340026855
  }
}
------------------------------------------------------------

[10] 查询: 未来一周天气怎么样
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.3994617462158203
  }
}
------------------------------------------------------------

[11] 查询: 未来两周天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+2\"}}"
    ],
    "inference_time": 0.36794400215148926
  }
}
------------------------------------------------------------

[12] 查询: 过去一周天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.3690667152404785
  }
}
------------------------------------------------------------

[13] 查询: 过去两周天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+2\"}}"
    ],
    "inference_time": 0.36710095405578613
  }
}
------------------------------------------------------------

[14] 查询: 深圳今天的天气怎么样
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"伦敦\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.40787577629089355
  }
}
------------------------------------------------------------

[15] 查询: 深圳明天的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"明天\"}}"
    ],
    "inference_time": 0.3674607276916504
  }
}
------------------------------------------------------------

[16] 查询: 深圳后天的天气怎么样
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"后天\"}}"
    ],
    "inference_time": 0.3782958984375
  }
}
------------------------------------------------------------

[17] 查询: 广东今天的天气怎么样
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"广东\"}}"
    ],
    "inference_time": 0.3668067455291748
  }
}
------------------------------------------------------------

[18] 查询: 深圳市南山区今天的天气怎么样
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.36956000328063965
  }
}
------------------------------------------------------------

[19] 查询: 今天的温度
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询温度\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.32057762145996094
  }
}
------------------------------------------------------------

[20] 查询: 今天的日出时间
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询日出时间\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.333101749420166
  }
}
------------------------------------------------------------

[21] 查询: 今天的日落时间
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.3272418975830078
  }
}
------------------------------------------------------------

[22] 查询: 今天的空气湿度
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询空气湿度\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\", \"offset\": \"+0\"}}"
    ],
    "inference_time": 0.3692336082458496
  }
}
------------------------------------------------------------

[23] 查询: 今天的空气质量
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询空气质量\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"unit\": \"day\", \"value\": \"\"}}"
    ],
    "inference_time": 0.36212635040283203
  }
}
------------------------------------------------------------

[24] 查询: 今天的风速
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询风速\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.32636332511901855
  }
}
------------------------------------------------------------

[25] 查询: 今天的限行信息
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询限行\", \"arguments\": {\"intent\": \"查询限行\", \"domain\": \"限行\", \"room\": \"伦敦\", \"device\": \"\", \"scene\": \"\", \"pos\": \"伦敦\", \"unit\": \"pos\"}}"
    ],
    "inference_time": 0.4370841979980469
  }
}
------------------------------------------------------------

[26] 查询: 广州的限行信息
状态: ✅ 成功
请求耗时: 0.49秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询限行\", \"arguments\": {\"intent\": \"查询限行\", \"domain\": \"限行\", \"room\": \"广州\", \"device\": \"\", \"scene\": \"\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.4782681465148926
  }
}
------------------------------------------------------------

[27] 查询: 今天广州的限行信息
状态: ✅ 成功
请求耗时: 0.47秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询限行\", \"arguments\": {\"intent\": \"查询限行\", \"domain\": \"限行\", \"room\": \"伦敦\", \"device\": \"\", \"scene\": \"\", \"pos\": \"伦敦\", \"offset\": \"\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.46360206604003906
  }
}
------------------------------------------------------------

[28] 查询: 化妆指数
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询化妆指数\", \"arguments\": {\"intent\": \"查询化妆指数\", \"domain\": \"指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3551661968231201
  }
}
------------------------------------------------------------

[29] 查询: 紫外线指数
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询紫外线指数\", \"arguments\": {\"intent\": \"查询紫外线指数\", \"domain\": \"紫外线\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.35356926918029785
  }
}
------------------------------------------------------------

[30] 查询: 感冒指数
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询感冒指数\", \"arguments\": {\"intent\": \"查询感冒指数\", \"domain\": \"指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3522303104400635
  }
}
------------------------------------------------------------

[31] 查询: 洗车指数
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询洗车指数\", \"arguments\": {\"intent\": \"查询洗车指数\", \"domain\": \"洗车指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3804597854614258
  }
}
------------------------------------------------------------

[32] 查询: 穿衣指数
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询穿衣指数\", \"arguments\": {\"intent\": \"查询穿衣指数\", \"domain\": \"指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3553130626678467
  }
}
------------------------------------------------------------

[33] 查询: 运动指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询运动指数\", \"arguments\": {\"intent\": \"查询运动指数\", \"domain\": \"运动指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.36104679107666016
  }
}
------------------------------------------------------------

[34] 查询: 钓鱼指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询钓鱼指数\", \"arguments\": {\"intent\": \"查询钓鱼指数\", \"domain\": \"钓鱼指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3605213165283203
  }
}
------------------------------------------------------------

[35] 查询: 交通指数
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询交通指数\", \"arguments\": {\"intent\": \"查询交通指数\", \"domain\": \"交通\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.35381078720092773
  }
}
------------------------------------------------------------

[36] 查询: 过敏指数
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询过敏指数\", \"arguments\": {\"intent\": \"查询过敏指数\", \"domain\": \"过敏指数\", \"value\": \"\", \"room\": \"伦敦\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3612210750579834
  }
}
------------------------------------------------------------

[37] 查询: 打开主卧的筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[38] 查询: 打开主卧的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:主卧;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[39] 查询: 打开明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[40] 查询: 打开筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[41] 查询: 打开全屋的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:全屋;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[42] 查询: 关闭主卧的筒灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:主卧;device:筒灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[43] 查询: 关闭主卧的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:主卧;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[44] 查询: 关闭明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[45] 查询: 关闭过道三开
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭通断器\", \"domain\": \"通断器\", \"room\": \"过道\", \"device\": \"三开\"}}"
    ],
    "inference_time": 0.40316224098205566
  }
}
------------------------------------------------------------

[46] 查询: 关闭全屋的明装筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:全屋;device:明装筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[47] 查询: 主卧的吸顶灯一亮度调高一点
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.40166521072387695
  }
}
------------------------------------------------------------

[48] 查询: 主卧的明装筒灯亮度调高一点
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4175276756286621
  }
}
------------------------------------------------------------

[49] 查询: 主卧的灯亮度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高亮度;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[50] 查询: 主卧的吸顶灯一亮度调到最高
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.4284086227416992
  }
}
------------------------------------------------------------

[51] 查询: 主卧的明装筒灯亮度调到最高
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.41521334648132324
  }
}
------------------------------------------------------------

[52] 查询: 主卧的灯亮度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高亮度;domain:灯;room:主卧;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[53] 查询: 调高吸顶灯一的亮度
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.39576053619384766
  }
}
------------------------------------------------------------

[54] 查询: 调高明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3891904354095459
  }
}
------------------------------------------------------------

[55] 查询: 调高全屋的明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39716196060180664
  }
}
------------------------------------------------------------

[56] 查询: 调高全屋灯的亮度
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.36881160736083984
  }
}
------------------------------------------------------------

[57] 查询: 吸顶灯一的亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.41685986518859863
  }
}
------------------------------------------------------------

[58] 查询: 明装筒灯的亮度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.41678619384765625
  }
}
------------------------------------------------------------

[59] 查询: 全屋的明装筒灯的亮度调到最高
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4155592918395996
  }
}
------------------------------------------------------------

[60] 查询: 全屋灯的亮度调到最高
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"setHigh\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.38501763343811035
  }
}
------------------------------------------------------------

[61] 查询: 主卧的吸顶灯一亮度调低一点
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.3960001468658447
  }
}
------------------------------------------------------------

[62] 查询: 主卧的明装筒灯亮度调低一点
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.38739562034606934
  }
}
------------------------------------------------------------

[63] 查询: 主卧的灯亮度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低亮度;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[64] 查询: 主卧的吸顶灯一亮度调到最低
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.41808462142944336
  }
}
------------------------------------------------------------

[65] 查询: 主卧的明装筒灯亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3884620666503906
  }
}
------------------------------------------------------------

[66] 查询: 主卧的灯亮度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低亮度;domain:灯;room:主卧;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[67] 查询: 调低吸顶灯一的亮度
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.3938784599304199
  }
}
------------------------------------------------------------

[68] 查询: 调低明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.389251708984375
  }
}
------------------------------------------------------------

[69] 查询: 调低全屋明装筒灯的亮度
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39613938331604004
  }
}
------------------------------------------------------------

[70] 查询: 调低全屋灯的亮度
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3675410747528076
  }
}
------------------------------------------------------------

[71] 查询: 吸顶灯一的亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"吸顶灯一\"}}"
    ],
    "inference_time": 0.3887486457824707
  }
}
------------------------------------------------------------

[72] 查询: 明装筒灯的亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3880765438079834
  }
}
------------------------------------------------------------

[73] 查询: 全屋明装筒灯的亮度调到最低
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.3882899284362793
  }
}
------------------------------------------------------------

[74] 查询: 全屋灯的亮度调到最低
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.36038899421691895
  }
}
------------------------------------------------------------

[75] 查询: 主卧的明装筒灯一亮度设成80%
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4095900058746338
  }
}
------------------------------------------------------------

[76] 查询: 主卧的明装筒灯亮度设成80%
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4092991352081299
  }
}
------------------------------------------------------------

[77] 查询: 主卧的灯亮度亮度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3818395137786865
  }
}
------------------------------------------------------------

[78] 查询: 明装筒灯一亮度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3805215358734131
  }
}
------------------------------------------------------------

[79] 查询: 明装筒灯亮度为80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3801863193511963
  }
}
------------------------------------------------------------

[80] 查询: 全屋的明装筒灯亮度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3827483654022217
  }
}
------------------------------------------------------------

[81] 查询: 全屋灯的亮度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.38175392150878906
  }
}
------------------------------------------------------------

[82] 查询: 主卧的明装筒灯一色温调高一点
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"%\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.42848777770996094
  }
}
------------------------------------------------------------

[83] 查询: 主卧的明装筒灯色温调高一点
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.42453503608703613
  }
}
------------------------------------------------------------

[84] 查询: 主卧的明装筒灯一色温调到最高
状态: ✅ 成功
请求耗时: 0.47秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100%\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.45758676528930664
  }
}
------------------------------------------------------------

[85] 查询: 主卧的明装筒灯色温调到最高
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.44222116470336914
  }
}
------------------------------------------------------------

[86] 查询: 主卧色温调高一点
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧吸顶灯\"}}"
    ],
    "inference_time": 0.40201783180236816
  }
}
------------------------------------------------------------

[87] 查询: 明装筒灯一的色温调高一点
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"5000\", \"room\": \"客厅\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.43629980087280273
  }
}
------------------------------------------------------------

[88] 查询: 主卧色温调到最高
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"色温\", \"value\": \"100\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.40026187896728516
  }
}
------------------------------------------------------------

[89] 查询: 明装筒灯一的色温调到最高
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.4311347007751465
  }
}
------------------------------------------------------------

[90] 查询: 明装筒灯的色温调高一点
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"100\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.42416906356811523
  }
}
------------------------------------------------------------

[91] 查询: 调高全屋明装筒灯的色温
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4028029441833496
  }
}
------------------------------------------------------------

[92] 查询: 明装筒灯的色温调到最高
状态: ✅ 成功
请求耗时: 0.55秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"setHigh\", \"room\": \"默认\", \"device\": \"明装筒灯\", \"scene\": \"\", \"pos\": \"\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.5441954135894775
  }
}
------------------------------------------------------------

[93] 查询: 全屋明装筒灯的色温调到最高
状态: ✅ 成功
请求耗时: 0.50秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"10000\", \"room\": \"默认\", \"device\": \"明装筒灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.48879456520080566
  }
}
------------------------------------------------------------

[94] 查询: 调高全屋灯的色温
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.37518835067749023
  }
}
------------------------------------------------------------

[95] 查询: 主卧的明装筒灯一色温调低一点
状态: ✅ 成功
请求耗时: 0.55秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.540381669998169
  }
}
------------------------------------------------------------

[96] 查询: 主卧的明装筒灯色温调低一点
状态: ✅ 成功
请求耗时: 0.55秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.5396597385406494
  }
}
------------------------------------------------------------

[97] 查询: 主卧的明装筒灯一色温调到最低
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"0\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.445178747177124
  }
}
------------------------------------------------------------

[98] 查询: 主卧的明装筒灯色温调到最低
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"最低\", \"room\": \"卧室\", \"device\": \"明装筒灯\", \"unit\": \"色温\"}}"
    ],
    "inference_time": 0.4501986503601074
  }
}
------------------------------------------------------------

[99] 查询: 主卧色温调低一点
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"默认\"}}"
    ],
    "inference_time": 0.3806023597717285
  }
}
------------------------------------------------------------

[100] 查询: 明装筒灯一的色温调低一点
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.43559694290161133
  }
}
------------------------------------------------------------

[101] 查询: 主卧色温调到最低
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"0\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.40740251541137695
  }
}
------------------------------------------------------------

[102] 查询: 明装筒灯一的色温调到最低
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"最低\", \"room\": \"默认\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.40828633308410645
  }
}
------------------------------------------------------------

[103] 查询: 明装筒灯的色温调低一点
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.39457249641418457
  }
}
------------------------------------------------------------

[104] 查询: 调低全屋明装筒灯的色温
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4031541347503662
  }
}
------------------------------------------------------------

[105] 查询: 调低全屋灯的色温
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.37372565269470215
  }
}
------------------------------------------------------------

[106] 查询: 全屋明装筒灯的色温调到最低
状态: ✅ 成功
请求耗时: 0.50秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"明装筒灯\", \"scene\": \"\", \"pos\": \"\", \"unit\": \"timeRange\"}}"
    ],
    "inference_time": 0.48937273025512695
  }
}
------------------------------------------------------------

[107] 查询: 全屋灯的色温调到最低
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3768742084503174
  }
}
------------------------------------------------------------

[108] 查询: 主卧的明装筒灯一色温设成3700
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.4166717529296875
  }
}
------------------------------------------------------------

[109] 查询: 主卧的明装筒灯色温设成3700
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColor温\", \"arguments\": {\"intent\": \"调色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"卧室\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.41603660583496094
  }
}
------------------------------------------------------------

[110] 查询: 主卧色温设成3700
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"色温\", \"value\": \"3700\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.4078507423400879
  }
}
------------------------------------------------------------

[111] 查询: 明装筒灯一的色温设成3700
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"客厅\", \"device\": \"明装筒灯一\"}}"
    ],
    "inference_time": 0.4247782230377197
  }
}
------------------------------------------------------------

[112] 查询: 明装筒灯的色温设成3700
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"默认\", \"device\": \"明装筒灯\"}}"
    ],
    "inference_time": 0.41585302352905273
  }
}
------------------------------------------------------------

[113] 查询: 全屋明装筒灯的色温设成3700
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColor温\", \"arguments\": {\"intent\": \"调色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3939933776855469
  }
}
------------------------------------------------------------

[114] 查询: 全屋灯的色温设成3700
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setColorTemperature\", \"arguments\": {\"intent\": \"设置色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.39710068702697754
  }
}
------------------------------------------------------------

[115] 查询: 打开主卧的一路通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:主卧;device:一路通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[116] 查询: 打开主卧的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[117] 查询: 打开通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[118] 查询: 打开通断器一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;device:通断器一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[119] 查询: 打开全屋的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开通断器;domain:通断器;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[120] 查询: 打开6路通断器灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;device:6路通断器灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[121] 查询: 打开海上的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:海上;device:海上的灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[122] 查询: 关闭主卧的一路通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:主卧;device:一路通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[123] 查询: 关闭主卧的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[124] 查询: 关闭通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[125] 查询: 关闭通断器一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;device:通断器一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[126] 查询: 关闭全屋的通断器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭通断器;domain:通断器;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[127] 查询: 关闭6路通断器灯一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;device:6路通断器灯一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[128] 查询: 打开主卧的纱帘
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧纱帘\"}}"
    ],
    "inference_time": 0.38016462326049805
  }
}
------------------------------------------------------------

[129] 查询: 打开主卧的布帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧布帘\"}}"
    ],
    "inference_time": 0.349273681640625
  }
}
------------------------------------------------------------

[130] 查询: 打开主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[131] 查询: 打开窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[132] 查询: 打开纱帘
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.32619285583496094
  }
}
------------------------------------------------------------

[133] 查询: 打开布帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3483693599700928
  }
}
------------------------------------------------------------

[134] 查询: 打开窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[135] 查询: 打开全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[136] 查询: 关闭主卧的纱帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧纱帘\"}}"
    ],
    "inference_time": 0.35323572158813477
  }
}
------------------------------------------------------------

[137] 查询: 关闭主卧的布帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧布帘\"}}"
    ],
    "inference_time": 0.34924840927124023
  }
}
------------------------------------------------------------

[138] 查询: 关闭主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[139] 查询: 关闭窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[140] 查询: 关闭纱帘
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.32739734649658203
  }
}
------------------------------------------------------------

[141] 查询: 关闭布帘
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3208632469177246
  }
}
------------------------------------------------------------

[142] 查询: 关上窗帘
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.32105374336242676
  }
}
------------------------------------------------------------

[143] 查询: 关闭全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[144] 查询: 暂停主卧的纱帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"暂停窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧纱帘\"}}"
    ],
    "inference_time": 0.3508336544036865
  }
}
------------------------------------------------------------

[145] 查询: 暂停主卧的布帘
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"暂停窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"主卧布帘\"}}"
    ],
    "inference_time": 0.3487417697906494
  }
}
------------------------------------------------------------

[146] 查询: 暂停主卧的窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[147] 查询: 暂停窗帘电机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘电机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[148] 查询: 暂停纱帘
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.32760095596313477
  }
}
------------------------------------------------------------

[149] 查询: 暂停布帘
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"暂停窗帘\", \"domain\": \"窗帘\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3220064640045166
  }
}
------------------------------------------------------------

[150] 查询: 暂停窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[151] 查询: 暂停全屋的窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:全屋"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[152] 查询: 主卧的纱帘开合度设成80%
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"卧室纱帘\", \"value\": \"80\"}}"
    ],
    "inference_time": 0.42613887786865234
  }
}
------------------------------------------------------------

[153] 查询: 主卧的布帘开合度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"布帘\", \"value\": \"80\"}}"
    ],
    "inference_time": 0.38298463821411133
  }
}
------------------------------------------------------------

[154] 查询: 主卧的窗帘电机开合度设成80%
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"窗帘电机\", \"value\": \"80\"}}"
    ],
    "inference_time": 0.3838467597961426
  }
}
------------------------------------------------------------

[155] 查询: 主卧的开合度设成80%
状态: ✅ 成功
请求耗时: 0.45秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"通断器\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"卧室通断器\"}}"
    ],
    "inference_time": 0.4385824203491211
  }
}
------------------------------------------------------------

[156] 查询: 窗帘电机开合度设成80%
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"窗帘\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3954129219055176
  }
}
------------------------------------------------------------

[157] 查询: 纱帘开合度设成80%
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"窗帘\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3956131935119629
  }
}
------------------------------------------------------------

[158] 查询: 布帘开合度设成80%
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"窗帘\", \"value\": \"80%\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3950822353363037
  }
}
------------------------------------------------------------

[159] 查询: 全屋的开合度设成80%
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调大开合度\", \"domain\": \"通断器\", \"value\": \"80\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.41705822944641113
  }
}
------------------------------------------------------------

[160] 查询: 打开主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[161] 查询: 打开主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[162] 查询: 打开窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[163] 查询: 打开窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[164] 查询: 打开全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[165] 查询: 打开窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[166] 查询: 关闭主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[167] 查询: 关闭主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[168] 查询: 关闭窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[169] 查询: 关闭窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[170] 查询: 关闭全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[171] 查询: 关闭窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[172] 查询: 暂停主卧的窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[173] 查询: 暂停主卧的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:主卧;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[174] 查询: 暂停窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[175] 查询: 暂停窗帘面板一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘面板一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[176] 查询: 暂停全屋的窗帘面板
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;room:全屋;device:窗帘面板"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[177] 查询: 暂停窗帘一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:暂停窗帘;domain:窗帘;device:窗帘一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[178] 查询: 回家模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[179] 查询: 到家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[180] 查询: 回来了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[181] 查询: 回家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:回家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[182] 查询: 离家模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[183] 查询: 我走了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[184] 查询: 出门
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[185] 查询: 上班去了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[186] 查询: 外出
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[187] 查询: 我离家了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:离家模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[188] 查询: 旅游
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"查询天气状况\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.38852620124816895
  }
}
------------------------------------------------------------

[189] 查询: 用餐模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:用餐模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[190] 查询: 我要吃饭了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:用餐模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[191] 查询: 会客模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:会客模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[192] 查询: 有客人来了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:会客模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[193] 查询: 来客人了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:会客模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[194] 查询: 晨起模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:晨起模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[195] 查询: 我要起床了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:晨起模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[196] 查询: 我醒了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:晨起模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[197] 查询: 睡眠模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:睡眠模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[198] 查询: 我要睡觉了
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:睡眠模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[199] 查询: 执行全开模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:全开模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[200] 查询: 执行全关模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开场景;domain:场景;scene:全关模式"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[201] 查询: 打开主卧的空调一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧;device:空调一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[202] 查询: 打开主卧的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[203] 查询: 打开主卧的主卧空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧;device:主卧空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[204] 查询: 打开主卧空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧;device:主卧空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[205] 查询: 打开客厅东空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:客厅;device:客厅东空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[206] 查询: 打开客厅西空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:客厅;device:客厅西空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[207] 查询: 打开空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[208] 查询: 打开全家的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[209] 查询: 关闭主卧的空调一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调;room:主卧;device:空调一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[210] 查询: 关闭主卧的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[211] 查询: 关闭空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[212] 查询: 关闭全家的空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭空调;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[213] 查询: 主卧的空调一的温度设成26度
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"主卧空调一\", \"value\": \"26\"}}"
    ],
    "inference_time": 0.4455397129058838
  }
}
------------------------------------------------------------

[214] 查询: 主卧的空调温度设成26度
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26度\", \"room\": \"卧室\", \"device\": \"主卧空调\"}}"
    ],
    "inference_time": 0.42317986488342285
  }
}
------------------------------------------------------------

[215] 查询: 空调温度设成26度
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.36248183250427246
  }
}
------------------------------------------------------------

[216] 查询: 空调一的温度设成26度
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"空调1\"}}"
    ],
    "inference_time": 0.37572646141052246
  }
}
------------------------------------------------------------

[217] 查询: 全家的空调温度设成26度
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26度\", \"room\": \"全屋\", \"device\": \"\"}}"
    ],
    "inference_time": 0.37450361251831055
  }
}
------------------------------------------------------------

[218] 查询: 主卧的主卧空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.42秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"value\": \"高\", \"room\": \"卧室\", \"device\": \"主卧空调\"}}"
    ],
    "inference_time": 0.4103538990020752
  }
}
------------------------------------------------------------

[219] 查询: 主卧的空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[220] 查询: 空调一的温度调高一点
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.3807239532470703
  }
}
------------------------------------------------------------

[221] 查询: 空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[222] 查询: 全家的空调的温度调高一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[223] 查询: 主卧的主卧空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"max\", \"room\": \"卧室\", \"device\": \"主卧空调\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.423386812210083
  }
}
------------------------------------------------------------

[224] 查询: 主卧的空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:主卧;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[225] 查询: 空调一的温度调到最高
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"max\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.37517833709716797
  }
}
------------------------------------------------------------

[226] 查询: 空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[227] 查询: 全家的空调的温度调到最高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调高温度;domain:空调;room:all;value:max"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[228] 查询: 主卧的主卧空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"调低温度\", \"domain\": \"空调\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧空调\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.42009806632995605
  }
}
------------------------------------------------------------

[229] 查询: 主卧的空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[230] 查询: 空调一的温度调低一点
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"调低温度\", \"domain\": \"空调\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.3690199851989746
  }
}
------------------------------------------------------------

[231] 查询: 空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[232] 查询: 全家的空调的温度调低一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[233] 查询: 主卧的主卧空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"最低\", \"room\": \"卧室\", \"device\": \"主卧空调\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.4236421585083008
  }
}
------------------------------------------------------------

[234] 查询: 主卧的空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:主卧;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[235] 查询: 空调一的温度调到最低
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"最低\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.3754432201385498
  }
}
------------------------------------------------------------

[236] 查询: 空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[237] 查询: 全家的空调的温度调到最低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:调低温度;domain:空调;room:all;value:min"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[238] 查询: 空调一的工作模式设成制冷模式
状态: ✅ 成功
请求耗时: 0.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制冷模式\"}}"
    ],
    "inference_time": 0.28704261779785156
  }
}
------------------------------------------------------------

[239] 查询: 空调一的工作模式设成制热模式
状态: ✅ 成功
请求耗时: 0.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.28838109970092773
  }
}
------------------------------------------------------------

[240] 查询: 空调一的工作模式设成送风模式
状态: ✅ 成功
请求耗时: 0.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"送风模式\"}}"
    ],
    "inference_time": 0.28612732887268066
  }
}
------------------------------------------------------------

[241] 查询: 空调一的工作模式设成换气模式
状态: ✅ 成功
请求耗时: 0.29秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"换气模式\"}}"
    ],
    "inference_time": 0.2859063148498535
  }
}
------------------------------------------------------------

[242] 查询: 空调一的工作模式设成除湿模式
状态: ✅ 成功
请求耗时: 0.30秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"除湿模式\"}}"
    ],
    "inference_time": 0.2871510982513428
  }
}
------------------------------------------------------------

[243] 查询: 主卧的主卧空调的模式设成制冷模式
状态: ✅ 成功
请求耗时: 0.39秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"制冷\", \"room\": \"卧室\", \"device\": \"主卧空调\"}}"
    ],
    "inference_time": 0.37658119201660156
  }
}
------------------------------------------------------------

[244] 查询: 主卧的空调设成制冷模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置模式;domain:空调;room:主卧;value:制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[245] 查询: 空调设成制冷模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置模式;domain:空调;value:制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[246] 查询: 空调一设成制冷模式
状态: ✅ 成功
请求耗时: 0.29秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制冷模式\"}}"
    ],
    "inference_time": 0.282609224319458
  }
}
------------------------------------------------------------

[247] 查询: 全家的空调设成制冷模式
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置模式;domain:空调;room:all;value:制冷"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[248] 查询: 空调一的风速设成高速
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"风速\", \"value\": \"高速\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.40639305114746094
  }
}
------------------------------------------------------------

[249] 查询: 空调一的风速设成中速
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setWindSpeed\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"风速\", \"value\": \"中\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.39519786834716797
  }
}
------------------------------------------------------------

[250] 查询: 空调一的风速设成低速
状态: ✅ 成功
请求耗时: 0.41秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setWindSpeed\", \"arguments\": {\"intent\": \"调低风速\", \"domain\": \"风速\", \"value\": \"低\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.39693546295166016
  }
}
------------------------------------------------------------

[251] 查询: 空调一的风速设成自动
状态: ✅ 成功
请求耗时: 0.49秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setWindSpeed\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"风速\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"空调一\", \"scene\": \"\", \"pos\": \"\", \"unit\": \"timeRange\"}}"
    ],
    "inference_time": 0.48581790924072266
  }
}
------------------------------------------------------------

[252] 查询: 主卧的主卧空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"风速\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧空调\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.4307436943054199
  }
}
------------------------------------------------------------

[253] 查询: 主卧的空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:空调;room:主卧;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[254] 查询: 空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:空调;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[255] 查询: 空调一的风速设成自动
状态: ✅ 成功
请求耗时: 0.40秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setWindSpeed\", \"arguments\": {\"intent\": \"设置风速\", \"domain\": \"风速\", \"value\": \"自动\", \"room\": \"客厅\", \"device\": \"空调一\"}}"
    ],
    "inference_time": 0.39539170265197754
  }
}
------------------------------------------------------------

[256] 查询: 全家的空调的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:空调;room:all;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[257] 查询: 打开次卧的开关一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;room:次卧;device:开关一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[258] 查询: 打开次卧的开关
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开开关\", \"domain\": \"开关\", \"room\": \"次卧\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3336780071258545
  }
}
------------------------------------------------------------

[259] 查询: 打开客厅的过道三开
状态: ✅ 成功
请求耗时: 0.36秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开开关\", \"domain\": \"开关\", \"room\": \"客厅\", \"device\": \"过道三开\"}}"
    ],
    "inference_time": 0.34955859184265137
  }
}
------------------------------------------------------------

[260] 查询: 打开客厅的餐厅筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:客厅;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[261] 查询: 打开客厅的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:客厅;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[262] 查询: 打开客厅的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:客厅"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[263] 查询: 打开开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[264] 查询: 打开开关一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;device:开关一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[265] 查询: 打开全家的开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[266] 查询: 关闭主卧的主卧双开
状态: ✅ 成功
请求耗时: 0.37秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭开关\", \"domain\": \"开关\", \"room\": \"卧室\", \"device\": \"主卧双开\"}}"
    ],
    "inference_time": 0.3566439151763916
  }
}
------------------------------------------------------------

[267] 查询: 关闭主卧的开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[268] 查询: 关闭开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[269] 查询: 关闭开关一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;device:开关一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[270] 查询: 关闭全家的开关
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[271] 查询: 打开书房的开关三
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开开关;domain:开关;room:书房;device:开关三"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[272] 查询: 打开"餐厅筒灯"
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:餐厅;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[273] 查询: 关闭书房的"餐厅筒灯"
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:书房;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[274] 查询: 关闭客厅的餐厅筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:客厅;device:餐厅筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[275] 查询: 关闭客厅的筒灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:客厅;device:筒灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[276] 查询: 关闭客厅的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:客厅"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[277] 查询: 关闭开关三
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭开关;domain:开关;device:开关三"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[278] 查询: 打开主卧的插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;room:主卧;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[279] 查询: 打开主卧的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[280] 查询: 打开插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[281] 查询: 打开插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[282] 查询: 打开全家的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开插座;domain:插座;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[283] 查询: 关闭主卧的插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;room:主卧;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[284] 查询: 关闭主卧的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[285] 查询: 关闭插座一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;device:插座一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[286] 查询: 关闭插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[287] 查询: 关闭全家的插座
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭插座;domain:插座;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[288] 查询: 打开主卧的地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;room:主卧;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[289] 查询: 打开主卧的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[290] 查询: 打开地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[291] 查询: 打开地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[292] 查询: 打开全家的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开地暖;domain:地暖;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[293] 查询: 关闭主卧的地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;room:主卧;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[294] 查询: 关闭主卧的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[295] 查询: 关闭地暖一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;device:地暖一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[296] 查询: 关闭地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[297] 查询: 关闭全家的地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭地暖;domain:地暖;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[298] 查询: 打开主卧的新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[299] 查询: 打开新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[300] 查询: 打开新风一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;device:新风一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[301] 查询: 打开全家的新风机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;room:all;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[302] 查询: 关闭主卧的新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[303] 查询: 关闭新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[304] 查询: 关闭新风一
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;device:新风一"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[305] 查询: 关闭全家的新风机
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;room:all;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[306] 查询: 主卧新风的风速设成自动
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:自动"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[307] 查询: 主卧新风的风速设成高
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:高"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[308] 查询: 主卧新风的风速设成中
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:中"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[309] 查询: 主卧新风的风速设成低
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:设置风速;domain:新风;room:主卧;value:低"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[310] 查询: 新风机风速调高
状态: ✅ 成功
请求耗时: 0.44秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高风速\", \"domain\": \"新风\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.43517565727233887
  }
}
------------------------------------------------------------

[311] 查询: 新风机风速调低
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低风速\", \"domain\": \"新风\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"新风机\"}}"
    ],
    "inference_time": 0.4161343574523926
  }
}
------------------------------------------------------------

[312] 查询: 新风机打开杀菌
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[313] 查询: 新风机关闭杀菌
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭新风;domain:新风;device:新风机"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[314] 查询: 打开安防
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开安防;domain:安防"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[315] 查询: 关闭安防
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭安防;domain:安防"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[316] 查询: 小零小零
状态: ✅ 成功
请求耗时: 0.59秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"content\": \"您好！很高兴为您服务，请问需要我帮您做什么？\", \"domain\": \"闲聊\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\", \"scene\": \"\", \"pos\": \"\", \"offset\": \"\", \"unit\": \"\"}}"
    ],
    "inference_time": 0.5789599418640137
  }
}
------------------------------------------------------------

[317] 查询: 查询天气
状态: ✅ 成功
请求耗时: 0.34秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.3269071578979492
  }
}
------------------------------------------------------------

[318] 查询: 关闭全家的灯
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:关闭灯;domain:灯;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[319] 查询: 查询我的设备数量
状态: ✅ 成功
请求耗时: 0.46秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\", \"scene\": \"\", \"pos\": \"\", \"offset\": \"\", \"unit\": \"\"}}"
    ],
    "inference_time": 0.4497225284576416
  }
}
------------------------------------------------------------

[320] 查询: 退下吧
状态: ✅ 成功
请求耗时: 0.27秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"dialog\", \"arguments\": {\"intent\": \"终止对话\", \"value\": \"\", \"domain\": \"对话\"}}"
    ],
    "inference_time": 0.2576014995574951
  }
}
------------------------------------------------------------

[321] 查询: 重新开始
状态: ✅ 成功
请求耗时: 0.29秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"dialog\", \"arguments\": {\"intent\": \"重新开始\", \"content\": \"重新开始对话\", \"domain\": \"对话\"}}"
    ],
    "inference_time": 0.2866485118865967
  }
}
------------------------------------------------------------

[322] 查询: 介绍几个景点给我
状态: ✅ 成功
请求耗时: 0.48秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"我为您找到了伦敦塔桥、大英博物馆和伦敦眼等著名景点。\", \"offset\": \"+0\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.4698359966278076
  }
}
------------------------------------------------------------

[323] 查询: 你是谁
状态: ✅ 成功
请求耗时: 0.22秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "我是小灵，您的智能家居助手，可以帮您控制家中的各种设备。请问需要我帮您做什么？"
    ],
    "inference_time": 0.21123790740966797
  }
}
------------------------------------------------------------

[324] 查询: 油烟机风速调大一点
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[325] 查询: 电饭煲开始煮饭
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[326] 查询: 微波炉剩余烹饪时长
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[327] 查询: 洗衣机调到快速洗
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[328] 查询: 新风机设为睡眠模式
状态: ✅ 成功
请求耗时: 0.38秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\", \"room\": \"客厅\", \"device\": \"新风机\"}}"
    ],
    "inference_time": 0.3713397979736328
  }
}
------------------------------------------------------------

[329] 查询: 关闭卧室所有灯
状态: ✅ 成功
请求耗时: 0.33秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"\"}}"
    ],
    "inference_time": 0.3217625617980957
  }
}
------------------------------------------------------------

[330] 查询: 按摩椅开始按摩
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[331] 查询: 打开加湿器
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[332] 查询: 打开投影
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "NOT_SUPPORTED"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[333] 查询: 灯亮度和色温调低一点
状态: ✅ 成功
请求耗时: 0.67秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.660041093826294
  }
}
------------------------------------------------------------

[334] 查询: 灯亮度和色温调高一点
状态: ✅ 成功
请求耗时: 0.66秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"色温\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.65561842918396
  }
}
------------------------------------------------------------

[335] 查询: 灯亮度调到80%色温调到3700k
状态: ✅ 成功
请求耗时: 0.68秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"默认\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调色温\", \"domain\": \"灯\", \"value\": \"3700\", \"room\": \"默认\", \"device\": \"\"}}"
    ],
    "inference_time": 0.6739797592163086
  }
}
------------------------------------------------------------

[336] 查询: 打开主卧的灯然后亮度和色温调低一点
状态: ✅ 成功
请求耗时: 1.00秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"主卧灯\", \"value\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧灯\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧灯\"}}"
    ],
    "inference_time": 0.990736722946167
  }
}
------------------------------------------------------------

[337] 查询: 打开主卧的灯然后亮度和色温调高一点
状态: ✅ 成功
请求耗时: 1.08秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"主卧灯\", \"value\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧灯\"}, \"offset\": \"+1\", \"unit\": \"day\"}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高色温\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"卧室\", \"device\": \"主卧灯\"}}"
    ],
    "inference_time": 1.067652702331543
  }
}
------------------------------------------------------------

[338] 查询: 打开主卧的灯然后亮度调到80%色温调到3700k
状态: ✅ 成功
请求耗时: 0.70秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"主卧灯\", \"value\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"主卧灯\"}}"
    ],
    "inference_time": 0.6916875839233398
  }
}
------------------------------------------------------------

[339] 查询: 打开全家的灯并把亮度调到80%色温调到3700k
状态: ✅ 成功
请求耗时: 0.72秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"全屋\", \"device\": \"\"}}, {\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"全屋\", \"device\": \"\"}, \"offset\": \"+0\", \"unit\": \"day\"}}"
    ],
    "inference_time": 0.7122275829315186
  }
}
------------------------------------------------------------

[340] 查询: 空调的温度调低一点并调成制冷
状态: ✅ 成功
请求耗时: 0.70秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"调低温度\", \"domain\": \"空调\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"客厅空调\", \"unit\": \"day\"}}",
      "{\"name\": \"setMode\", \"arguments\": {\"intent\": \"设置模式\", \"domain\": \"模式\", \"value\": \"制冷\", \"room\": \"客厅\", \"device\": \"客厅空调\"}}"
    ],
    "inference_time": 0.6949653625488281
  }
}
------------------------------------------------------------

[341] 查询: 空调的温度调到26度模式调成制热
状态: ✅ 成功
请求耗时: 0.63秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26度\", \"room\": \"客厅\", \"device\": \"客厅空调\", \"scene\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.6199986934661865
  }
}
------------------------------------------------------------

[342] 查询: 空调的温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 0.58秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.5683298110961914
  }
}
------------------------------------------------------------

[343] 查询: 客厅空调的温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 0.57秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"setTemperature\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.5652780532836914
  }
}
------------------------------------------------------------

[344] 查询: 打开客厅的空调然后温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 0.83秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"温度\", \"value\": \"26\", \"room\": \"客厅\", \"device\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.8247373104095459
  }
}
------------------------------------------------------------

[345] 查询: 打开全家的空调然后温度调到26度模式调成制热风速调高
状态: ✅ 成功
请求耗时: 0.85秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"全屋\", \"device\": \"\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"温度\", \"value\": \"26\", \"room\": \"全屋\", \"device\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\"}}"
    ],
    "inference_time": 0.8411259651184082
  }
}
------------------------------------------------------------

[346] 查询: 打开灯和空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯",
      "intent:打开空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[347] 查询: 打开灯和地暖
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯",
      "intent:打开地暖;domain:地暖"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[348] 查询: 打开灯和新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风",
      "intent:打开灯;domain:灯"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[349] 查询: 打开空调和新风
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开新风;domain:新风",
      "intent:打开空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[350] 查询: 打开主卧的灯和空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧",
      "intent:打开灯;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[351] 查询: 打开全家的灯和空调
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:all",
      "intent:打开空调;domain:空调;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[352] 查询: 打开灯和空调还有窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯",
      "intent:打开窗帘;domain:窗帘",
      "intent:打开空调;domain:空调"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[353] 查询: 打开主卧的灯和空调还有窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开空调;domain:空调;room:主卧",
      "intent:打开窗帘;domain:窗帘;room:主卧",
      "intent:打开灯;domain:灯;room:主卧"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[354] 查询: 打开全家的灯和空调还有窗帘
状态: ✅ 成功
请求耗时: 0.01秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:打开灯;domain:灯;room:all",
      "intent:打开空调;domain:空调;room:all",
      "intent:打开窗帘;domain:窗帘;room:all"
    ],
    "inference_time": 0
  }
}
------------------------------------------------------------

[355] 查询: 打开主卧的所有设备
状态: ✅ 成功
请求耗时: 0.43秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开设备\", \"domain\": \"设备\", \"room\": \"卧室\", \"device\": \"\", \"value\": \"主卧灯\", \"scene\": \"\"}}"
    ],
    "inference_time": 0.4223792552947998
  }
}
------------------------------------------------------------

[356] 查询: 打开氛围灯然后亮度调低色温调低
状态: ✅ 成功
请求耗时: 0.70秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"氛围灯\", \"value\": \"\"}}, {\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"value\": \"\", \"room\": \"客厅\", \"device\": \"\"}, \"offset\": \"+1\"}}"
    ],
    "inference_time": 0.6885335445404053
  }
}
------------------------------------------------------------

[357] 查询: 打开氛围灯然后亮度调到50色温调到4000
状态: ✅ 成功
请求耗时: 0.64秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"氛围灯\"}}",
      "{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调亮度\", \"domain\": \"灯\", \"value\": \"50\", \"room\": \"客厅\", \"device\": \"\"}}"
    ],
    "inference_time": 0.6349387168884277
  }
}
------------------------------------------------------------

[358] 查询: 打开空调一工作模式调成制热温度调到26度风速调成高速
状态: ✅ 成功
请求耗时: 0.62秒
响应: {
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"客厅空调\", \"value\": \"\"}}",
      "{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"制热模式\", \"value\": \"\"}}"
    ],
    "inference_time": 0.6088042259216309
  }
}
------------------------------------------------------------

